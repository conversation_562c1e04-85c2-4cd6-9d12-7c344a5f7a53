// import 'regenerator-runtime/runtime.js';
import { SVG, off, on } from '@svgdotjs/svg.js';
import _ from 'lodash';
import './affix/draggable';
import { SET_ROOT_ACTION } from './store/actions';
import {
  SigmaRectangleShape,
  SigmaBlockShape,
  SigmaTextLabelShape,
  SigmaIconShape,
  SigmaImageShape,
  SigmaAreaShape,
  SigmaAutoScalingShape,
  SigmaAvailabilityZoneShape,
  SigmaProductShape,
  SigmaSecurityShape,
  SigmaBaseGroupShape,
  SigmaVPCShape,
  SigmaSubnetShape,
  SigmaSecurityGroupShape,
  SigmaLineShape as LineShape,
  SigmaRemarkNoteShape,
  SigmaCcnShape,
  SigmaTkeShape,
} from './shape';
import Sigmacore from './core';
import store from './store';
import { clearConnectLineToolBox } from './util/toolbox';
import {
  SIGMA_PRODUCT_SHAPE,
  SIGMA_SECURITY_SHAPE,
  BASE_GRID_X,
  BASE_GRID_Y,
  BASE_GRID_2D,
  SIGMA_GRAPH_MODE_2D,
  SIGMA_GRAPH_MODE_3D,
  SELECT_CURSOR,
  GLOBAL_EVENT_SUFFIX,
} from './util/constants';
import renders from './renders';
import { VnodeStickyUnit } from './affix/vnode-sticky-unit';
import { transform2DTo3D, transform3DTo2D, setUseLinePoints } from './util/tools';

function Sigma(container, options = {}) {
  this.VERSION = '0.0.7';
  console.log('sigma-editor 加载的版本 6.6.6');
  this.IS_IE = navigator.userAgent != null && navigator.userAgent.indexOf('MSIE') >= 0;
  this.components = [];
  this.core = null;
  this.container = container;
  if (!(options?.gain)) {
    // 画布站可视区大小的基础倍数
    options.gain = 15;
  }
  this.options = options;
  this.isDoubleTouch = false;
  // {
  //   mode: '3d' 3d | 2d
  //   callbacks: {
  //     onCheckedChange 当选中节点变化时
  //     onGraphChange 当绘图变化时
  //     customizeSave
  //     onShapeMouseRightClick 在元素上点击右键时
  //     onShapeMouseUp 鼠标在元素上抬起时
  //     onShapeClick 点击元素时
  //     onShapeDBLClick 双击元素时
  //     onShapeMove 当拖动元素时
  //     onShapeMoveStart 当元素开始拖动时
  //     onShapeMoveEnd 当元素拖动结束时
  //     onShapeDelete 当元素被移除时
  //     onDocClick 在文档上点击时
  //     onShapeBoxChange 当点击元素，右击菜单出现，用于追加元素
  //     onError = ({code, msg, data}) => {} // 当编辑器出现错误时
  //   }
  // }
  this.config = {
    backgroundColor: {
      default: '#ffffff',
      value: localStorage.getItem('sigma.backgroundColor') || null,
    },
    entireColor: {
      default: '#DCDCDC',
      value: localStorage.getItem('sigma.entireColor') || null,
    },
    quarterColor: {
      default: '#eeeeee',
      value: localStorage.getItem('sigma.quarterColor') || null,
    },
  };
  this.graph = {}; // id, name, description
  off(window, GLOBAL_EVENT_SUFFIX);
  this._init();
}

Sigma.prototype._init = function () {
  const { container, options } = this;
  if (!container) {
    console.error('容器初始化错误, 请检查');
    return;
  }
  container.tabIndex = -1;
  container.innerHTML = '';
  const { scale } = store.getState();
  const w = container.offsetWidth;
  const h = container.offsetHeight;
  const viewboxW = _.round(w * scale);
  const viewboxH = _.round(h * scale);
  const doc = new SVG().addTo(container)
    .attr({
      viewBox: `0 0 ${viewboxW} ${viewboxH}`,
      width: w,
      height: h,
      overflow: 'visible',
    });
  const defs = doc.defs();
  const g = doc.group();
  const dropzone = g.rect().attr({ 'g-name': 'dropzone' })
    .css('cursor', SELECT_CURSOR); // 拖拽区域 0
  const sence = g.group().attr({ 'pointer-events': 'none', 'g-name': 'sence' }); // 基础网格背景区域 1
  const grid = g.group().attr({ 'pointer-events': 'none', 'g-name': 'grid' }); // 大网格区域 3
  const gNetwork = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gNetwork' }); // vpc，子网，安全组 4
  const gRect = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gRect' }); // 矩形容器 5
  const gLine = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gLine' }); // 连线容器 6
  const gCircle = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gCircle' }); // 点容器 7
  const gProduct = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gProduct' }); // 产品图标容器 8
  const gImage = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gImage' }); // 图片容器 9
  const gIcon = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gIcon' }); // 图标容器 10
  const gText = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gText' }); // 文案容器 11
  const gCache = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gCache' }); // 暂存区 20

  const initialRoot = {
    root: container,
    doc,
    defs,
    g,
    dropzone,
    sence,
    grid,
    mode: options.mode === '2d' ? SIGMA_GRAPH_MODE_2D : SIGMA_GRAPH_MODE_3D,
    w,
    h,
    viewBox: { x: 0, y: 0, w: viewboxW, h: viewboxH },
    container: {
      gText,
      gImage,
      gIcon,
      gProduct,
      gRect,
      gNetwork,
      gLine,
      gCircle,
      gCache,
      0: dropzone,
      1: sence,
      3: grid,
      4: gNetwork,
      5: gRect,
      6: gLine,
      7: gCircle,
      8: gProduct,
      9: gImage,
      10: gIcon,
      11: gText,
      20: gCache,
    },
    options,
  };
  store.dispatch({ type: SET_ROOT_ACTION, value: initialRoot });
  const { callbacks = {} } = options;
  if (callbacks.onDocClick) {
    doc.on('mousedown', (e) => {
      // 有时候对齐线不会消失，此处做一下处理
      this.core.lines.clear();
      callbacks.onDocClick(e);
    });
  }
  const core = new Sigmacore(options);
  this.core = core;
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      core,
    },
  });

  this._initSence();
  this._initFun();
  this._initComponents();
  this.zoomGraphFit();
  this.cmdStack = this.core.cmdStack;
};

Sigma.prototype._initSence = function () {
  const baseUnit = this.options.gain;
  const { mode, defs, w, h, sence, dropzone, grid } = store.getState();
  const { backgroundColor, quarterColor, entireColor } = this.config;
  // 拖拽区域设置
  dropzone.attr({
    width: baseUnit * w,
    height: baseUnit * h,
    x: -(baseUnit / 2) * w,
    y: -(baseUnit / 2) * h,
    fill: backgroundColor.value || backgroundColor.default,
  });
  sence.clear();
  grid.clear();
  defs.clear();

  // 背景设置（小网格）
  const gSence = sence.group().attr({
    class: 'gSence-bg',
  });
  const gGrid = grid.group().attr({
    class: 'gGrid-bg',
  });
  const entirePattern = defs
    .pattern()
    .attr({ id: 'entire-grid', patternUnits: 'userSpaceOnUse' });
  const quarterPattern = defs
    .pattern()
    .attr({ id: 'quarter-grid', patternUnits: 'userSpaceOnUse' });

  // 补充安全组件选中滤镜
  const filterBright75 = new SVG(`<filter id="bright75">
  <feComponentTransfer>
    <feFuncR type="linear" slope="0.6" />
    <feFuncG type="linear" slope="0.6" />
    <feFuncB type="linear" slope="0.6" />
  </feComponentTransfer>
</filter>`);
  defs.add(filterBright75);

  const pointString = `${-baseUnit * w * 100} ${-baseUnit * h * 100}, ${baseUnit * w * 100} ${-baseUnit * h * 100}, ${baseUnit * w * 100} ${
    baseUnit * h * 100
  }, ${-baseUnit * w * 100} ${baseUnit * h * 100}`;
  gGrid.polygon().attr({
    points: pointString,
    fill: 'url(#entire-grid)',
    class: 'entire-grid',
  });
  gSence.polygon().attr({
    points: pointString,
    fill: 'url(#quarter-grid)',
    class: 'quarter-grid',
  });
  if (mode === SIGMA_GRAPH_MODE_2D) {
    entirePattern.attr({ x: 0, y: 0, width: 90, height: 90 });
    entirePattern.path().attr({
      d: 'M 0 0 L 90 0 90 90 0 90 z',
      stroke: entireColor.value || entireColor.default,
      'stroke-width': 1,
      fill: 'none',
      class: 'entirePattern',
    });
    quarterPattern.attr({ x: 0, y: 0, width: 90, height: 90 });
    quarterPattern.path().attr({
      d: 'M -45 45 L 135 45',
      stroke: quarterColor.value || quarterColor.default,
      'stroke-width': 1,
      class: 'quarterPattern',
    });
    quarterPattern.path().attr({
      d: 'M 45 -45 L 45 135',
      stroke: quarterColor.value || quarterColor.default,
      'stroke-width': 1,
      class: 'quarterPattern',
    });
  }
  if (mode === SIGMA_GRAPH_MODE_3D) {
    entirePattern.attr({ x: 0, y: 37, width: 128, height: 74 });
    entirePattern.path().attr({
      d: 'M 64 0 L 128 37 64 74 0 37 z',
      stroke: entireColor.value || entireColor.default,
      'stroke-width': 1,
      fill: 'none',
      class: 'entirePattern',
    });
    quarterPattern.attr({ x: 0, y: 37, width: 128, height: 74 });
    quarterPattern.path().attr({
      d: 'M 0 0 L 128 74',
      stroke: quarterColor.value || quarterColor.default,
      'stroke-width': 1,
      class: 'quarterPattern',
    });
    quarterPattern.path().attr({
      d: 'M 128 0 L 0 74',
      stroke: quarterColor.value || quarterColor.default,
      'stroke-width': 1,
      class: 'quarterPattern',
    });
  }
};
Sigma.prototype._initFun = function () {
  this.__zoom();
  this.__drag();
  this.__initWindowFun();
};
Sigma.prototype._initComponents = function () {
  const rectangleShape = new SigmaRectangleShape();
  const blockShape = new SigmaBlockShape();
  const textLabelShape = new SigmaTextLabelShape();
  const imageShape = new SigmaImageShape();
  const iconShape = new SigmaIconShape();
  const baseGroupShape = new SigmaBaseGroupShape();
  const areaShape = new SigmaAreaShape();
  const autoScalingShape = new SigmaAutoScalingShape();
  const availabilityZoneShape = new SigmaAvailabilityZoneShape();
  const securityGroupShape = new SigmaSecurityGroupShape();
  const vpcShape = new SigmaVPCShape();
  const subnetShape = new SigmaSubnetShape();
  const remarkNoteShape = new SigmaRemarkNoteShape();
  const ccnShape = new SigmaCcnShape();
  const tkeShape = new SigmaTkeShape();
  const components = [
    rectangleShape,
    blockShape,
    textLabelShape,
    iconShape,
    imageShape,
    baseGroupShape,
    areaShape,
    autoScalingShape,
    availabilityZoneShape,
    securityGroupShape,
    vpcShape,
    subnetShape,
    remarkNoteShape,
    ccnShape,
    tkeShape,
  ];

  this.components = components;
};

Sigma.prototype.__zoom = function () {
  const { doc, root } = store.getState();
  const { callbacks = {}, gain } = this.options;
  const { onScaleChange } = callbacks;
  let isMoving;
  let distance;
  let isRotated = false;

  function mouseWheel(e) {
    let nextScale;
    let pageX = 0;
    let pageY = 0;
    const { viewBox, maxScale, minScale, w, h, step, scale, doc, dropzone, sence, grid } = store.getState();
    if (e.length === 2) {
      // 此时是移动端双指指令，需要判断放大还是缩小
      const currDistance = getDistance(e);
      const delta = currDistance - distance > 0 ? -1 : 1;
      nextScale = _.round(scale + delta * step, 2);
      nextScale = Math.max(minScale, Math.min(maxScale, nextScale));
      pageX = (e[0].pageX + e[1].pageX) / 2;
      pageY = (e[0].pageY + e[1].pageY) / 2;
      if (isRotated) {
        // 如果旋转了，就以屏幕中心缩放
        this.scaleStep(delta / 10);
        return;
      }
    } else {
      const ctrlKey = e.ctrlKey || e.metaKey;
      if (!ctrlKey) return;

      const delta = (e.wheelDelta || e.detail) > 0 ? -1 : 1;
      nextScale = scale < 1 ? _.round(scale + delta * step / 4, 2) : _.round(scale + delta * step, 2);
      nextScale = Math.max(minScale, Math.min(maxScale, nextScale));
      pageX = e.pageX;
      pageY = e.pageY;
    }

    const nextW = _.round(nextScale * w, 2);
    const nextH = _.round(nextScale * h, 2);
    const { x: viewboxX, y: viewboxY, w: viewboxW, h: viewboxH } = viewBox;
    const { x: mouseX, y: mouseY } = doc.point(pageX, pageY);
    const ratioL = (mouseX - viewboxX) / viewboxW;
    const ratioT = (mouseY - viewboxY) / viewboxH;
    const nextX = mouseX - ratioL * nextW;
    const nextY = mouseY - ratioT * nextH;


    const nextViewBox = `${nextX} ${nextY} ${nextW} ${nextH}`;
    store.dispatch({
      type: SET_ROOT_ACTION,
      value: {
        viewBox: { x: nextX, y: nextY, w: nextW, h: nextH },
        scale: nextScale,
      },
    });
    doc.attr({ viewBox: nextViewBox });
    let dropzoneX = 0;
    let dropzoneY = 0;
    if (nextX <= 0) {
      dropzoneX = -(Math.abs(nextW) * (gain / 2) + Math.abs(nextX));
    } else {
      if (nextX <= (Math.abs(nextW) * (gain / 2))) {
        dropzoneX = -((Math.abs(nextW) * (gain / 2)) - nextX);
      } else {
        dropzoneX = nextX - (Math.abs(nextW) * (gain / 2));
      }
    }

    if (nextY <= 0) {
      dropzoneY = -(Math.abs(nextH) * (gain / 2) + Math.abs(nextY));
    } else {
      if (nextY <= (Math.abs(nextH) * (gain / 2))) {
        dropzoneY = -((Math.abs(nextH) * (gain / 2)) - nextY);
      } else {
        dropzoneY = nextY - (Math.abs(nextH) * (gain / 2));
      }
    }

    dropzone.attr({
      x: dropzoneX,
      y: dropzoneY,
      width: Math.abs(nextW) * gain,
      height: Math.abs(nextH) * gain,
    });
    const gSence = sence.findOne('.gSence-bg');
    const gGrid = grid.findOne('.gGrid-bg');

    const pointString = `${-Math.abs(nextW) * gain * 100} ${-Math.abs(nextH) * gain * 100}, ${Math.abs(nextW) * gain * 100} ${-Math.abs(nextH) * gain * 100}, ${Math.abs(nextW) * gain * 100} ${
      Math.abs(nextH) * gain * 100
    }, ${-Math.abs(nextW) * gain * 100} ${Math.abs(nextH) * gain * 100}`;
    gSence.findOne('.quarter-grid').attr({
      points: pointString,
      fill: 'url(#quarter-grid)',
      class: 'quarter-grid',
      x: dropzoneX,
      y: dropzoneY,
    });
    gGrid.findOne('.entire-grid').attr({
      points: pointString,
      fill: 'url(#entire-grid)',
      class: 'entire-grid',
      x: dropzoneX,
      y: dropzoneY,
    });

    onScaleChange && onScaleChange(nextScale);
    clearConnectLineToolBox();

    // 触发 doc move 事件
    callbacks.onDocMove && callbacks.onDocMove();
  }

  const getDistance = e => Math.sqrt((e[0].clientX - e[1].clientX) ** 2
        + (e[0].clientY - e[1].clientY) ** 2);

  const optWheel = _.throttle((ev) => {
    if (isMoving) return;
    ev.preventDefault();
    let event = ev;
    if (ev?.type === 'touchmove') {
      if (ev?.touches?.length === 2) {
        event = ev.touches;
      } else if (ev?.targetTouches?.length === 2) {
        event = ev.targetTouches;
      } else {
        return;
      }
    }
    isMoving = true;
    requestAnimationFrame(() => {
      mouseWheel(event);
      isMoving = false;
    });
  }, 16);

  const changeClass = (productKey, type = 'add') => {
    const selector = `[key="${productKey}"]`;
    const shapeBodys = doc.find(selector);
    shapeBodys?.forEach((shapeBody) => {
      if (type === 'add') {
        shapeBody?.style('pointer-events', 'none');
      } else {
        shapeBody?.style('pointer-events', 'auto');
      }
    });
  };

  const initPos = (ev) => {
    let touches = [];
    if (ev.type === 'touchstart' && (ev.touches.length === 2 || ev.targetTouches.length === 2)) {
      // 给所有节点禁用鼠标事件
      this.isDoubleTouch = true;
      this.core.isDoubleTouch = true;
      if (ev.touches.length === 2) {
        touches = ev.touches;
      }
      if (ev.targetTouches.length === 2) {
        touches = ev.targetTouches;
      }
      const allShapes = this.core.data.shapes;
      Object.keys(allShapes).forEach((key) => {
        changeClass(key);
      });
      distance = getDistance(touches);
      doc.on('touchmove', optWheel);
      isRotated = root.classList.contains('isRotated');
    }
  };

  const resetPos = (ev) => {
    if (ev.type === 'touchend') {
      this.isDoubleTouch = false;
      this.core.isDoubleTouch = false;
      const allShapes = this.core.data.shapes;
      Object.keys(allShapes).forEach((key) => {
        changeClass(key, 'remove');
      });
      distance = 0;
      doc.off('touchmove', optWheel);
    }
  };

  doc.on('wheel', optWheel);
  doc.on('touchstart', initPos);
  doc.on('touchend', resetPos);
};

Sigma.prototype.__drag = function () {
  const { doc, dropzone, root } = store.getState();
  const { callbacks = {} } = this.options;
  const pos = {};
  let nextX; let nextY;
  let flag = false;
  let dragable = false;
  let isRotated = false;
  const that = this;
  let isMouseDown = false;

  function mouseMove(e) {
    if (!isMouseDown) return;
    let event = e;
    if (e.type === 'touchmove') {
      if (e.targetTouches.length === 1) {
        event = e.targetTouches[0];
        e.preventDefault();
      } else {
        return;
      }
      if (that.isDoubleTouch) {
        return;
      }
    }
    const { viewBox, scale, dropzone } = store.getState();
    const { x: viewboxX, y: viewboxY, w, h } = viewBox;
    let offsetX;
    let offsetY;
    if (!isRotated) {
      offsetX = pos.x - event.clientX;
      offsetY = pos.y - event.clientY;
    } else {
      offsetY = event.clientX - pos.x;
      offsetX = pos.y - event.clientY;
    }

    const dropzoneWidth = dropzone.node.width.baseVal.value;
    const dropzoneHeight = dropzone.node.height.baseVal.value;
    const dropzoneX = dropzone.node.x.baseVal.value;
    const dropzoneY = dropzone.node.y.baseVal.value;
    nextX = _.round(viewboxX + offsetX * scale, 0);
    nextY = _.round(viewboxY + offsetY * scale, 0);
    dragable = false;
    if (dropzoneY >= 0 && nextY >= 0) {
      if (nextY <= dropzoneY) {
        nextY = dropzoneY;
        return;
      }
      if (nextY - dropzoneY + h >= dropzoneHeight) {
        nextY = dropzoneHeight - h + dropzoneY;
        return;
      }
    }
    if (dropzoneY < 0 && nextY >= 0) {
      const totalHeight = Math.abs(dropzoneY) + nextY + h;
      if (totalHeight >= dropzoneHeight) {
        nextY = dropzoneHeight -  Math.abs(dropzoneY) - h;
        return;
      }
    }
    if (dropzoneY < 0 && nextY < 0) {
      if (nextY <= dropzoneY) {
        nextY = dropzoneY;
        return;
      }
      if (Math.abs(dropzoneY) - Math.abs(nextY)  + h >= dropzoneHeight) {
        nextY = -(dropzoneHeight - Math.abs(dropzoneY) - h);
        return;
      }
    }

    if (dropzoneX >= 0 && nextX >= 0) {
      if (nextX <= dropzoneX) {
        nextX = dropzoneX;
        return;
      }
      if (nextX - dropzoneX + w >= dropzoneWidth) {
        nextX = dropzoneWidth - w + dropzoneX;
        return;
      }
    }
    if (dropzoneX < 0 && nextX >= 0) {
      const totalWidth = Math.abs(dropzoneX) + nextX + w;
      if (totalWidth >= dropzoneWidth) {
        nextX = dropzoneWidth -  Math.abs(dropzoneX) - w;
        return;
      }
    }
    if (dropzoneX < 0 && nextX < 0) {
      if (nextX <= dropzoneX) {
        nextX = dropzoneX;
        return;
      }
      if (Math.abs(dropzoneX) - Math.abs(nextX) + w >= dropzoneWidth) {
        nextX = -(dropzoneWidth - Math.abs(dropzoneX) - w);
        return;
      }
    }
    dragable = true;
    const nextViewBox = `${nextX} ${nextY} ${viewBox.w} ${viewBox.h}`;
    doc.attr({ viewBox: nextViewBox });
    // 触发 doc move 事件
    callbacks.onDocMove && callbacks.onDocMove();
  }

  const funcOptMove = _.throttle(mouseMove, 16);

  function mouseUp() {
    isMouseDown = false;
    const { viewBox } = store.getState();
    if (nextX !== undefined && nextY !== undefined && dragable) {
      store.dispatch({
        type: SET_ROOT_ACTION,
        value: {
          viewBox: { x: nextX, y: nextY, w: viewBox.w, h: viewBox.h },
        },
      });
      nextX = undefined;
      nextY = undefined;
    }
    dropzone.off('mousemove', funcOptMove);
    dropzone.off('touchmove', funcOptMove);
    if (!flag) {
      dropzone.off('mousedown', mouseDown);
      dropzone.off('touchstart', mouseDown);
    }
  }

  function mouseDown(e) {
    isMouseDown = true;
    // 检测当前是否是旋转的画布
    isRotated = root.classList.contains('isRotated');
    clearConnectLineToolBox();
    let event = e;
    if (e.type === 'touchstart') {
      if (e.targetTouches.length === 1) {
        event = e.targetTouches[0];
      } else {
        return;
      }
      if (that.isDoubleTouch) {
        return;
      }
    } else {
      if (e.ctrlKey || e.metaKey) {
        return;
      }
    }

    pos.x = event.clientX;
    pos.y = event.clientY;
    dropzone.on('mousemove', funcOptMove);
    dropzone.on('touchmove', funcOptMove);
  }

  on(window, `mouseup${GLOBAL_EVENT_SUFFIX}`, mouseUp);
  on(window, `touchend${GLOBAL_EVENT_SUFFIX}`, mouseUp);

  dropzone.on('touchstart', (e) => {
    setTimeout(() => {
      mouseDown(e);
    }, 0);
  });

  on(window, `keydown${GLOBAL_EVENT_SUFFIX}`, (kev) => {
    if (flag) return;
    if (kev.code === 'Space') {
      flag = true;
      dropzone.css('cursor', 'grab');
      nextX = undefined;
      nextY = undefined;
      dropzone.on('mousedown', mouseDown);
    }
  });
  on(window, `keyup${GLOBAL_EVENT_SUFFIX}`, (kev) => {
    const isGrab = sessionStorage.getItem('sigmaIsGrab') === 'true';
    if (!isGrab && kev.code === 'Space' && flag) {
      flag = false;
      dropzone.css('cursor', SELECT_CURSOR);
      mouseUp();
    }
  });
};

Sigma.prototype.__setExportArea = function (w2, h2) {
  const width = w2;
  const height = h2;
  const {
    viewBox: { x, y, w, h },
    doc,
  } = store.getState();
  const entireGrid = doc.findOne('.entire-grid');
  const quarterGrid = doc.findOne('.quarter-grid');
  if (width && height) {
    const gapx = (w - width) / 2;
    const gapy = (h - height) / 2;
    const limitX = gapx > 200 ? 200 : gapx;
    const limitY = gapy > 200 ? 200 : gapy;
    const p1x = x + limitX;
    const p1y = y + limitY;
    const pointString = `${p1x} ${p1y}, ${p1x + width} ${p1y}, ${p1x + width} ${
      p1y + height
    }, ${p1x} ${p1y + height}`;
    entireGrid.attr({ points: pointString });
    quarterGrid.attr({
      points: pointString,
      style: 'outline: 2px solid #dddddd',
    });
  } else {
    const pointString = `${-10 * w} ${-10 * h}, ${10 * w} ${-10 * h}, ${
      10 * w
    } ${10 * h}, ${-10 * w} ${10 * h}`;
    entireGrid.attr({ points: pointString });
    quarterGrid.attr({ points: pointString, style: null });
  }
};

Sigma.prototype.__initWindowFun = function () {
  const that = this;
  this.container.classList.add('__sigma__container');
  this.container.id = 'sigma-container';
  const style = document.querySelector('style[data-sigma="built-in"]');
  if (!style) {
    const style = document.createElement('style');
    style.setAttribute('data-sigma', 'built-in');
    style.innerText = '#sigma-container svg {overflow: auto;}';
    document.body.appendChild(style);
  }

  on(window, `keydown${GLOBAL_EVENT_SUFFIX}`, (e) => {
    const keyCode = e.key;
    const ctrlKey = e.ctrlKey || e.metaKey;
    switch (true) {
      case ctrlKey && (keyCode === '+' || keyCode === '='):
        e.preventDefault();
        that.scaleStep(-0.05);
        break;
      case ctrlKey && keyCode === '-':
        e.preventDefault();
        that.scaleStep(0.05);
        break;
      case ctrlKey && keyCode === '0':
        e.preventDefault();
        that.setScale(1);
        break;
      default:
    }
  });
  on(window, `resize${GLOBAL_EVENT_SUFFIX}`, () => {
    this.refreshViewport();
  });
};

Sigma.prototype.refreshViewport = function () {
  const { callbacks = {} } = this.options;
  const { doc, scale, viewBox } = store.getState();
  const { container } = this;
  const w = container.offsetWidth;
  const h = container.offsetHeight;
  const viewboxW = _.round(w * scale);
  const viewboxH = _.round(h * scale);
  doc.attr({
    viewBox: `${viewBox.x} ${viewBox.y} ${viewboxW} ${viewboxH}`,
    width: w,
    height: h,
  });
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      w,
      h,
      viewBox: { x: viewBox.x, y: viewBox.y, w: viewboxW, h: viewboxH },
    },
  });

  // 触发 doc move 事件
  callbacks.onDocMove && callbacks.onDocMove();
};

// 本身对外暴露接口
Sigma.prototype.getComponents = function () {
  return this.components;
};
Sigma.prototype.getLayers = function () {
  const {
    sence,
    grid,
    container: {
      gText,
      gImage,
      gIcon,
      gNetwork,
      gProduct,
      // gArea,
      gRect,
      // gScale,
      gLine,
      // gCircle,
    },
  } = store.getState();
  return [
    {
      key: 'sigma-layer-Sence',
      name: 'Sence',
      cName: '背景网格',
      layer: sence,
    },
    { key: 'sigma-layer-Grid', name: 'Grid', cName: '基础网格', layer: grid },
    { key: 'sigma-layer-Network', name: 'Network', cName: '组', layer: gNetwork },
    { key: 'sigma-layer-Text', name: 'Text', cName: '文本', layer: gText },
    { key: 'sigma-layer-Image', name: 'Image', cName: '图片', layer: gImage },
    { key: 'sigma-layer-Icon', name: 'Icon', cName: '图标', layer: gIcon },
    {
      key: 'sigma-layer-Product',
      name: 'Product',
      cName: '产品图标',
      layer: gProduct,
    },
    // { key: 'sigma-layer-Area', name: 'Area', cName: '区域', layer: gArea },
    { key: 'sigma-layer-Rect', name: 'Rect', cName: '区域', layer: gRect },
    // {
    //   key: 'sigma-layer-AutoScale',
    //   name: 'AutoScale',
    //   cName: '弹性伸缩',
    //   layer: gScale,
    // },
    { key: 'sigma-layer-Line', name: 'Line', cName: '连线', layer: gLine },
    // { key: 'sigma-layer-Circle', name: 'Circle', cName: '点', layer: gCircle },
  ];
};

// 注册自定义基础元素
Sigma.prototype.registerComponent = function (Construct, renderer) {
  if (typeof Construct !== 'function') {
    console.error('注册的自定义元素必须是一个构造函数, 返回之中');
    return false;
  }
  if (
    typeof renderer.render !== 'function'
    || typeof renderer.rerender !== 'function'
    || typeof renderer.exports !== 'function'
    || typeof renderer.transform !== 'function'
    || typeof renderer.reduction !== 'function'
    || typeof renderer.getflat !== 'function'
  ) {
    console.error('渲染器必须包括 render、rerender、exports、transform、reduction、getflat 属性方法');
    return false;
  }
  const component = new Construct();
  const _vnode = component.create();
  this.components.push(component);
  renders.register(_vnode.type, renderer);
};

Sigma.prototype.getRenders = function () {
  return renders;
};

Sigma.prototype.toggleMode = function () {
  setUseLinePoints();
  const { mode } = store.getState();
  const nextMode = mode === SIGMA_GRAPH_MODE_2D ? SIGMA_GRAPH_MODE_3D : SIGMA_GRAPH_MODE_2D;
  store.dispatch({ type: SET_ROOT_ACTION, value: { mode: nextMode } });
  this._initSence();
  this.core._toggleMode();
  this.zoomGraphFit();
  localStorage.setItem('sigma_mode', nextMode);
  setTimeout(() => {
    setUseLinePoints(false);
  }, 0);
};

Sigma.prototype.setScale = function (scale, callback, option) {
  const step = scale - store.getState().scale;
  this.scaleStep(step, callback, option);
};

Sigma.prototype.scaleStep = function (step, callback, option) {
  const { callbacks = {}, gain } = this.options;
  const { onScaleChange, onDocMove } = callbacks;
  const { doc, w, h, viewBox: { x: _x, y: _y }, scale, maxScale, minScale, dropzone, sence, grid } = store.getState();
  let nextScale = _.round(scale + step, 2);

  const optionMaxScale = option?.maxScale ?? maxScale;
  nextScale = Math.min(optionMaxScale > maxScale ? optionMaxScale : maxScale, Math.max(minScale, nextScale));
  if (nextScale === scale) return;

  const nextW = nextScale * w;
  const nextH = nextScale * h;

  const x = _x - w * step / 2;
  const y = _y - h * step / 2;

  const nextViewBox = `${x} ${y} ${nextW} ${nextH}`;
  doc.animate({
    duration: 500,
    when: 'now',
  }).attr({ viewBox: nextViewBox })
    .after(() => callback?.());

  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      viewBox: { x, y, w: nextW, h: nextH },
      scale: nextScale,
    },
  });

  let dropzoneX = 0;
  let dropzoneY = 0;
  if (x <= 0) {
    dropzoneX = -(Math.abs(nextW) * (gain / 2) + Math.abs(x));
  } else {
    if (x <= (Math.abs(nextW) * (gain / 2))) {
      dropzoneX = -((Math.abs(nextW) * (gain / 2)) - x);
    } else {
      dropzoneX = x - (Math.abs(nextW) * (gain / 2));
    }
  }

  if (y <= 0) {
    dropzoneY = -(Math.abs(nextH) * (gain / 2) + Math.abs(y));
  } else {
    if (y <= (Math.abs(nextH) * (gain / 2))) {
      dropzoneY = -((Math.abs(nextH) * (gain / 2)) - y);
    } else {
      dropzoneY = y - (Math.abs(nextH) * (gain / 2));
    }
  }
  dropzone.animate({
    duration: 500,
    when: 'now',
  }).attr({
    x: dropzoneX,
    y: dropzoneY,
    width: Math.abs(nextW) * gain,
    height: Math.abs(nextH) * gain,
  });
  const gSence = sence.findOne('.gSence-bg');
  const gGrid = grid.findOne('.gGrid-bg');

  const pointString = `${-Math.abs(nextW) * gain * 100} ${-Math.abs(nextH) * gain * 100}, ${Math.abs(nextW) * gain * 100} ${-Math.abs(nextH) * gain * 100}, ${Math.abs(nextW) * gain * 100} ${
    Math.abs(nextH) * gain * 100
  }, ${-Math.abs(nextW) * gain * 100} ${Math.abs(nextH) * gain * 100}`;

  gSence.findOne('.quarter-grid').attr({
    points: pointString,
    fill: 'url(#quarter-grid)',
    class: 'quarter-grid',
    x: dropzoneX,
    y: dropzoneY,
  });
  gGrid.findOne('.entire-grid').attr({
    points: pointString,
    fill: 'url(#entire-grid)',
    class: 'entire-grid',
    x: dropzoneX,
    y: dropzoneY,
  });

  // 触发 doc move 事件
  onDocMove && onDocMove();

  onScaleChange && onScaleChange(nextScale);
  clearConnectLineToolBox();
};

Sigma.prototype.loadShapes = function (shapes) {
  const components = [];
  for (let i = 0; i < shapes.length; i++) {
    const shape = shapes[i];
    if (shape.type === SIGMA_PRODUCT_SHAPE) {
      components.push(new SigmaProductShape(shape));
    }
    if (shape.type === SIGMA_SECURITY_SHAPE) {
      components.push(new SigmaSecurityShape(shape));
    }
  }
  this.components = [...this.components, ...components];
};

// core 对外方法暴露
Sigma.prototype.setStyleNode = function (data, keys, opt) {
  this.core.setStyles(data, keys, opt);
};
Sigma.prototype.createNode = function (shape, options = {}) {
  const { customize, label, position, key } = options;
  const vnode = shape.create(key);
  const vnodes = [vnode];
  if (options) {
    const standardPosition = this.core.__getStandardPosition(position);
    vnode.position = { ...vnode.position, ...standardPosition };
  }
  if (customize) {
    if (_.isObject(customize)) {
      vnode.customize = _.cloneDeep(customize);
    } else {
      console.error('自定义数据必须是一个对象');
      return;
    }
  }
  if (shape?.type === SIGMA_PRODUCT_SHAPE) {
    this.cmdStack.saveAction({
      groupBegin: true,
    });
  }
  this.core.add(vnodes);

  const hasLabel = label && _.isString(label) && label.length > 0;
  hasLabel && this.createNodeLabel(vnode, label);

  // 清除对齐线
  this.core.lines.clear();

  return vnode.key;
};
Sigma.prototype.replaceNode = function (shape, options = {}) {
  const { customize } = options;
  const vnode = shape.create();
  if (customize) {
    if (_.isObject(customize)) {
      vnode.customize = _.cloneDeep(customize);
    } else {
      console.error('自定义数据必须是一个对象');
    }
  }
  this.core.replace(vnode);
};
Sigma.prototype.cloneNode = function () {
  this.core.clone();
};
Sigma.prototype.cutNode = function () {
  this.core.cut();
};
Sigma.prototype.copyNode = function () {
  this.core.copy();
};
Sigma.prototype.deleteNode = function () {
  this.core.delete();
};
Sigma.prototype.lockNode = function () {
  this.core.lock();
};
Sigma.prototype.unlockNode = function () {
  this.core.unlock();
};
Sigma.prototype.toggleLockNode = function () {
  this.core.toggleLock();
};
Sigma.prototype.descriptNode = function (desc) {
  this.core.descript(desc);
};
Sigma.prototype.redo = function () {
  this.core.redo();
};
Sigma.prototype.undo = function () {
  this.core.undo();
};
Sigma.prototype.raiseShape = function () {
  this.core.raise();
};
Sigma.prototype.lowerShape = function () {
  this.core.lower();
};
Sigma.prototype.setNodePosition = function (position = {}, key) {
  this.core.setPosition(position, key);
};

Sigma.prototype.setVnodePosition = function (vnode, position) {
  if (!(vnode && vnode.component)) {
    console.error('vnode component 未渲染！');
    return;
  }
  const sp = this.core.__getStandardPosition(position);
  vnode.position = sp;
  vnode.component.fire('position-change');
};

Sigma.prototype.toggleLayerVisable = function (layer) {
  if (layer.visible()) {
    layer.hide();
  } else {
    layer.show();
  }
};
Sigma.prototype.setBackgroundGrid = function (width, height) {
  if (_.isNumber(width) && _.isNumber(height)) {
    this.config.backgroundGrid = { width, height };
    this.__setExportArea(width * 1.28, height * 1.28);
  } else {
    this.config.backgroundGrid = null;
    this.__setExportArea();
  }
};

// 获取内容最小x, y, 最大 w, h;
// 获取基础容器的宽高，位置。
Sigma.prototype.getBackgroundGrid = function () {
  const { g } = store.getState();
  const g2 = g.clone();
  g2.insertAfter(g);
  const grid = g2.findOne('[g-name="grid"]');
  const sence = g2.findOne('[g-name="sence"]');
  const dropzone = g2.findOne('[g-name="dropzone"]');
  grid.remove();
  sence.remove();
  dropzone.remove();
  const { backgroundGrid } = this.config;
  let x = Infinity;
  let y = Infinity;
  let w = -1;
  let h = -1;
  const { w: gw, h: gh, x: gx, y: gy } = g2.bbox();
  if (backgroundGrid) {
    const { width, height } = backgroundGrid;
    w = width;
    h = height;
  } else {
    w = gw;
    h = gh;
  }
  x = gx;
  y = gy;
  g2.remove();
  return { w, h, x, y };
};

// 如果仅包含选中，需要重新调整宽高
Sigma.prototype.getRenderedXML = async function (config = {}) {
  const {
    width: _width,
    height: _height,
    includeGrid = true,
    onlySelection,
    exportMode,
  } = config;
  const { gain } = this.options;
  function initSize(size = 0, d = 200) {
    return Math.max(size || 0, d);
  }
  const width = initSize(_width);
  const height = initSize(_height);

  const { quarterColor, entireColor } = this.config;
  const mode = exportMode ? exportMode : store.getState().mode;
  let outerBox = document.createElement('div');

  const { x, y, w: _w, h: _h } = this.getBackgroundGrid();
  const w = initSize(_w, width);
  const h = initSize(_h, height);
  const doc = new SVG().addTo(outerBox)
    .attr({
      viewBox: `${x} ${y} ${w} ${h}`,
      width: w,
      height: h,
    });
  const defs = doc.defs();
  const g = doc.group();
  const sence = g.group().attr({ 'pointer-events': 'none', 'g-name': 'sence' }); // 基础网格背景区域 1
  const grid = g.group().attr({ 'pointer-events': 'none', 'g-name': 'grid' }); // 大网格区域 3
  const gNetwork = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gNetwork' }); // 各种组 4
  const gRect = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gRect' }); // 矩形容器 5
  const gLine = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gLine' }); // 连线容器 6
  const gCircle = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gCircle' }); // 点容器 7
  const gProduct = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gProduct' }); // 产品图标容器 8
  const gImage = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gImage' }); // 图片容器 9
  const gIcon = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gIcon' }); // 图标容器 10
  const gText = g.group().attr({ 'pointer-events': 'auto', 'g-name': 'gText' }); // 文案容器 11
  const gCache = g
    .group()
    .attr({ 'pointer-events': 'auto', 'g-name': 'gCache' }); // 暂存区 20

  if (!includeGrid) {
    sence.remove();
    grid.remove();
  } else {
    const gSence = sence.group();
    const gGrid = grid.group();
    const entirePattern = defs
      .pattern()
      .attr({ id: 'entire-grid', patternUnits: 'userSpaceOnUse' });
    const quarterPattern = defs
      .pattern()
      .attr({ id: 'quarter-grid', patternUnits: 'userSpaceOnUse' });
    const pointString = `${-gain * 100 * width} ${-gain * 100 * height}, ${gain * 100 * width} ${
      -gain * 100 * height
    }, ${gain * 100 * width} ${gain * 100 * height}, ${-gain * 100 * width} ${gain * 100 * height}`;
    gGrid.polygon().attr({
      points: pointString,
      fill: 'url(#entire-grid)',
      class: 'entire-grid',
    });
    gSence.polygon().attr({
      points: pointString,
      fill: 'url(#quarter-grid)',
      class: 'quarter-grid',
    });
    if (mode === SIGMA_GRAPH_MODE_3D) {
      entirePattern.attr({ x: 0, y: 37, width: 128, height: 74 });
      entirePattern.path().attr({
        d: 'M 64 0 L 128 37 64 74 0 37 z',
        stroke: '#DCDCDC',
        'stroke-width': 1,
        fill: 'none',
      });
      quarterPattern.attr({ x: 0, y: 37, width: 128, height: 74 });
      quarterPattern
        .path()
        .attr({ d: 'M 0 0 L 128 74', stroke: '#eee', 'stroke-width': 1 });
      quarterPattern
        .path()
        .attr({ d: 'M 128 0 L 0 74', stroke: '#eee', 'stroke-width': 1 });
    } else {
      entirePattern.attr({ x: 0, y: 0, width: 90, height: 90 });
      entirePattern.path().attr({
        d: 'M 0 0 L 90 0 90 90 0 90 z',
        stroke: entireColor.value || entireColor.default,
        'stroke-width': 1,
        fill: 'none',
        class: 'entirePattern',
      });
      quarterPattern.attr({ x: 0, y: 0, width: 90, height: 90 });
      quarterPattern.path().attr({
        d: 'M -45 45 L 135 45',
        stroke: quarterColor.value || quarterColor.default,
        'stroke-width': 1,
        class: 'quarterPattern',
      });
      quarterPattern.path().attr({
        d: 'M 45 -45 L 45 135',
        stroke: quarterColor.value || quarterColor.default,
        'stroke-width': 1,
        class: 'quarterPattern',
      });
    }
  }
  await this.core._pureRender(
    {
      1: sence,
      3: grid,
      4: gNetwork,
      5: gRect,
      6: gLine,
      7: gCircle,
      8: gProduct,
      9: gImage,
      10: gIcon,
      11: gText,
      20: gCache,
    },
    { onlySelection, mode },
  );
  const xml = outerBox.innerHTML;
  outerBox = null;
  return xml;
};

// 清空页面，清空数据
Sigma.prototype.clearPaint = function () {
  this.core?.allShapeChecked();
  // 清空画布需要强制删除所有节点
  this.core?.delete({ force: true });
};

Sigma.prototype.resetViewbox = function () {
  const { callbacks = {} } = this.options;
  const {
    doc,
    viewBox: { w, h },
  } = store.getState();
  const viewbox = { x: 0, y: 0, w, h };
  doc.attr({ viewBox: `0 0 ${w}, ${h}` });
  store.dispatch({ type: SET_ROOT_ACTION, value: { viewBox: viewbox } });
  // 触发 doc move 事件
  callbacks.onDocMove && callbacks.onDocMove();
};

// 使用数据初始化
Sigma.prototype.initWithGraphData = function (graph) {
  setUseLinePoints();
  const initData = unescape(graph.content);
  this.initWithTPLGraphData(initData);
  setUseLinePoints(false);
};

/**
 * 使用原始数据进行初始化
 * @param {*} content 使用字符串初始化
 */
Sigma.prototype.initWithTPLGraphData = function (content) {
  try {
    setUseLinePoints();
    const initData = unescape(content);
    if (initData && initData !== 'null') {
      const originDetail = JSON.parse(initData);
      const components = this.getComponents();
      // 旧方案在detail添加了这个字段处理，如果有就直接干掉
      const { svgMap } = originDetail;
      if (svgMap) {
        delete originDetail.svgMap;
      }
      // 如果找不到对应的svg数据，使用默认svg渲染
      const defaultData = {
        d3: '<g class="shape-body">\n          <path class="dark outline" d="M28.55 67.373l21.5 12.422 21.5-12.422V42.161l-21.5-12.064-21.5 12.064v25.212zm21.5 14.577a1 1 0 0 1-.5-.134l-22.5-13a.997.997 0 0 1-.5-.866V41.575a1 1 0 0 1 .51-.872l22.5-12.625c.305-.17.675-.17.979 0l22.5 12.625a1 1 0 0 1 .51.872V67.95c0 .358-.19.688-.5.866l-22.5 13a1 1 0 0 1-.5.134z"/>\n          <path class="fill-light" d="M27.55 41.575l22.5 12.813 22.5-12.813-22.5-12.625z"/>\n          <path class="dark outline" d="M28.565 41.578L50.05 53.813l21.485-12.235L50.05 29.523 28.565 41.578zM50.05 54.89a.494.494 0 0 1-.247-.066l-22.5-12.813a.5.5 0 0 1 .002-.87l22.5-12.626a.508.508 0 0 1 .49 0l22.5 12.625a.498.498 0 0 1 .002.871l-22.5 12.813a.494.494 0 0 1-.247.066z"/>\n          <path class="fill-dark" d="M50.05 80.95V54.388l-22.5-12.813V67.95z"/>\n          <path class="dark outline" d="M28.05 67.662l21.5 12.422V54.68l-21.5-12.245v25.227zm22 13.788a.496.496 0 0 1-.25-.067l-22.5-13a.5.5 0 0 1-.25-.433V41.575a.5.5 0 0 1 .747-.434l22.5 12.813a.501.501 0 0 1 .253.435V80.95a.5.5 0 0 1-.5.5z"/>\n          <path class="dark" d="M39.985 59.636c-.229-.272-.544-.522-.943-.75l-2.064-1.178v3.48l2.064 1.177c.465.266.843.35 1.133.257.29-.094.436-.47.436-1.126 0-.738-.209-1.358-.626-1.86zm-4.052-3.691l3.451 1.967c.682.389 1.233.955 1.653 1.697.42.743.63 1.57.63 2.482 0 .784-.189 1.359-.563 1.727-.374.368-.947.332-1.719-.108l-2.407-1.374v4.245l-1.045-.595V55.945zM52.12 42.054l2.854-3.476-5.584 1.899 2.73 1.577zm3.647-4.737l1.331.767-6.333 7.939-1.29-.745 1.933-2.357-3.412-1.97-3.786 1.288-1.208-.698 12.765-4.224z"/>\n          <path class="fill-dark" d="M50.05 80.95l22.5-13V41.575l-22.5 12.813z"/>\n          <path class="dark outline" d="M50.55 54.68v25.404l21.5-12.422V42.436L50.55 54.68zm-.5 26.77a.5.5 0 0 1-.5-.5V54.39c0-.18.097-.346.253-.435l22.5-12.813a.498.498 0 0 1 .747.435V67.95a.5.5 0 0 1-.25.432l-22.5 13a.497.497 0 0 1-.25.068z"/>\n          <path class="dark" d="M63.355 58.08c-.204.01-.483.132-.84.364l-1.836 1.2v3.016l1.837-1.199a3.45 3.45 0 0 0 1.009-.997c.258-.394.386-.875.386-1.445 0-.639-.186-.953-.556-.939zm-3.604 1.157l3.07-2.006c.605-.396 1.096-.498 1.47-.305.373.193.559.684.559 1.474 0 .681-.167 1.381-.5 2.102a4.122 4.122 0 0 1-1.528 1.755l-2.142 1.4v3.43l-.93.607v-8.457z"/>\n        </g>',
        d2: '<svg width="32" height="32" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 7H0V0h7v7zM5.001 5H2V2h3.001v3zM11.5 7a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7zm0-2a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zM0 15h7V8H0v7zm2-1.999h3.001V10H2v3.001zM15 8v7H8V8h7zm-2 5.001h-3V10h3v3.001z" fill="#0052D9"/></svg>',
      };
      Object.values(originDetail)?.forEach((shape) => {
        if (shape.type === 'SIGMA_PRODUCT_SHAPE' && !shape?.data?.d2) {
          const currShape = components?.find(c => c?.name === shape?.name && shape.type === 'SIGMA_PRODUCT_SHAPE');
          if (shape.name === 'CCN') {
            const currShape = components?.find(c => c?.name === 'CCNS' && shape.type === 'SIGMA_PRODUCT_SHAPE');
            _.set(shape, 'name', 'CCNS');
            const svgData = currShape?.data ?? defaultData;
            _.set(shape, 'data', svgData);
          } else {
            const svgData = currShape?.data ?? defaultData;
            _.set(shape, 'data', svgData);
          }
        }
      });
      this.core._initRenderWithStringData(originDetail);
      this.zoomGraphFit();
    }
    setUseLinePoints(false);
  } catch (e) {
    console.error(e.message);
  }
};

/**
 * 获取本地字符串类型的原始数据
 * @returns 获取本地字符串类型的原始数据
 */
Sigma.prototype.getLocalGraphData = function (options) {
  return this.core.getGraphData(options);
};
/**
 * 获取所有的网络（区域类型）组件数据
 * @returns 组件列表（数据）[vnodes]
 */
Sigma.prototype.getNetworkShapes = function () {
  return this.core._getNetworkShapes();
};
/**
 * 给当前选中元素设置 group
 * @param {*} key groupVnode 的key值，string 类型
 */
// Sigma.prototype.setGroup = function (key) {
//   this.core.setGroup(key);
// };
/**
 * 给当前选中元素设置基础组
 * @param {*} key VPCVnode 的key值，string 类型
 */
Sigma.prototype.setBaseGroup = function (key, createSource) {
  return this.core.setBaseGroup(key, createSource);
};
/**
 * 给当前选中元素设置地域
 * @param {*} key VPCVnode 的key值，string 类型
 */
Sigma.prototype.setAreaGroup = function (key, createSource) {
  return this.core.setAreaGroup(key, createSource);
};
/**
 * 给当前选中元素设置可用区
 * @param {*} key VPCVnode 的key值，string 类型
 */
Sigma.prototype.setAvailabilityZoneGroup = function (key, createSource) {
  return this.core.setAvailabilityZoneGroup(key, createSource);
};
/**
 * 给当前选中元素设置 SecurityGroup
 * @param {*} key SecurityGroupVnode 的key值，string 类型
 */
Sigma.prototype.setSecurityGroup = function (key, createSource) {
  return this.core.setSecurityGroup(key, createSource);
};
/**
 * 给当前选中元素设置 VPCGroup
 * @param {*} key VPCVnode 的key值，string 类型
 */
Sigma.prototype.setVPCGroup = function (key, createSource) {
  return this.core.setVPCGroup(key, createSource);
};
/**
 * 给当前选中元素设置 SubnetGroup
 * @param {*} key SubnetVnode 的key值，string 类型
 */
Sigma.prototype.setSubnetGroup = function (key, createSource) {
  return this.core.setSubnetGroup(key, createSource);
};
/**
 * 给当前选中元素设置 CcnGroup
 * @param {*} key CcnGroup 的key值，string 类型
 */
Sigma.prototype.setCcnGroup = function (key, createSource) {
  return this.core.setCcnGroup(key, createSource);
};

/**
 * 给当前选中元素设置 tkegroup
 * @param {*} key tkegroup 的key值，string 类型
 */
Sigma.prototype.setTkeGroup = function (key, createSource) {
  return this.core.setTkeGroup(key, createSource);
};

Sigma.prototype.zoomGraphFit = function () {
  const { callbacks = {}, gain } = this.options;
  const { onScaleChange, onDocMove } = callbacks;
  const { mode, doc, w: cw, h: ch, dropzone, sence, grid } = store.getState();
  const { w, h, x, y } = this.getBackgroundGrid();
  let vx;
  let vy;
  let vw;
  let vh;
  let nextScale = 1;
  const is3D = mode === SIGMA_GRAPH_MODE_3D;
  const marginx = is3D ? 4 * BASE_GRID_X : 4 * BASE_GRID_2D;
  const marginy = is3D ? 4 * BASE_GRID_Y : 4 * BASE_GRID_2D;
  if (w !== 0 && h !== 0) {
    if (w + marginx > cw || h + marginy > ch) {
      // 超过大小需要缩放,缩放到刚好能放下即可
      // 先判断那边大，需要缩放大的那边
      const scalew = _.ceil((w + marginx) / cw, 1);
      const scaleh = _.ceil((h + marginy) / ch, 1);
      nextScale = scalew > scaleh ? scalew : scaleh;
      vw = _.round(nextScale * cw, 2);
      vh = _.round(nextScale * ch, 2);
      vx = x - (vw - w) / 2;
      vy = y - (vh - h) / 2;
    } else {
      vw = cw;
      vh = ch;
      vx = x - (cw - w) / 2;
      vy = y - (ch - h) / 2;
    }
  } else {
    vw = cw;
    vh = ch;
    vx = 0;
    vy = 0;
  }
  const nextViewBox = `${vx} ${vy} ${vw} ${vh}`;
  doc.attr({ viewBox: nextViewBox });
  let dropzoneX = 0;
  let dropzoneY = 0;
  if (vx <= 0) {
    dropzoneX = -(Math.abs(vw) * (gain / 2) + Math.abs(vx));
  } else {
    if (vx <= (Math.abs(vw) * (gain / 2))) {
      dropzoneX = -((Math.abs(vw) * (gain / 2)) - vx);
    } else {
      dropzoneX = vx - (Math.abs(vw) * (gain / 2));
    }
  }

  if (vy <= 0) {
    dropzoneY = -(Math.abs(vh) * (gain / 2) + Math.abs(vy));
  } else {
    if (vy <= (Math.abs(vh) * (gain / 2))) {
      dropzoneY = -((Math.abs(vh) * (gain / 2)) - vy);
    } else {
      dropzoneY = vy - (Math.abs(vh) * (gain / 2));
    }
  }
  dropzone.attr({
    x: dropzoneX,
    y: dropzoneY,
    width: Math.abs(vw) * gain,
    height: Math.abs(vh) * gain,
  });

  const gSence = sence.findOne('.gSence-bg');
  const gGrid = grid.findOne('.gGrid-bg');

  const pointString = `${-Math.abs(vw) * gain * 100} ${-Math.abs(vh) * gain * 100}, ${Math.abs(vw) * gain * 100} ${-Math.abs(vh) * gain * 100}, ${Math.abs(vw) * gain * 100} ${
    Math.abs(vh) * gain * 100
  }, ${-Math.abs(vw) * gain * 100} ${Math.abs(vh) * gain * 100}`;

  gSence.findOne('.quarter-grid').attr({
    points: pointString,
    fill: 'url(#quarter-grid)',
    class: 'quarter-grid',
  });
  gGrid.findOne('.entire-grid').attr({
    points: pointString,
    fill: 'url(#entire-grid)',
    class: 'entire-grid',
  });
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      viewBox: { x: vx, y: vy, w: vw, h: vh },
      scale: nextScale,
    },
  });

  // 触发 doc move 事件
  onDocMove && onDocMove();
  onScaleChange && onScaleChange(nextScale);
  clearConnectLineToolBox();
};

/**
 * 设置编辑器背景，网格颜色等
 * @param {*} data {}
 */
Sigma.prototype.setEditor = function (data) {
  const { backgroundColor, quarterColor, entireColor, remember } = data;
  const { doc, dropzone } = store.getState();
  if (backgroundColor) {
    dropzone.attr('fill', backgroundColor);
    if (remember) {
      localStorage.setItem('sigma.backgroundColor', backgroundColor);
    }
  }
  if (entireColor) {
    const entirePattern = doc.find('.entirePattern');
    entirePattern.attr('stroke', entireColor);
    if (remember) {
      localStorage.setItem('sigma.entireColor', entireColor);
    }
  }
  if (quarterColor) {
    const quarterPattern = doc.find('.quarterPattern');
    quarterPattern.attr('stroke', quarterColor);
    if (remember) {
      localStorage.setItem('sigma.quarterColor', quarterColor);
    }
  }
};
Sigma.prototype.resetEditor = function () {
  const { doc, dropzone } = store.getState();
  localStorage.removeItem('sigma.backgroundColor');
  localStorage.removeItem('sigma.quarterColor');
  localStorage.removeItem('sigma.entireColor');
  this.config.backgroundColor.value = null;
  this.config.entireColor.value = null;
  this.config.quarterColor.value = null;
  const entirePattern = doc.find('.entirePattern');
  const quarterPattern = doc.find('.quarterPattern');
  dropzone.attr('fill', this.config.backgroundColor.default);
  entirePattern.attr('stroke', this.config.entireColor.default);
  quarterPattern.attr('stroke', this.config.quarterColor.default);
};

Sigma.prototype.getReferOriginPosition = function () {
  const {
    viewBox: { x, y },
  } = store.getState();
  return { x, y };
};

// 自定义数据的设置、获取和重制。
Sigma.prototype.setCustomize = function (data, target) {
  this.core.setCustomize(data, target);
};
Sigma.prototype.resetCustomize = function (target) {
  this.core.resetCustomize(target);
};
Sigma.prototype.getCustomize = function (target) {
  this.core.getCustomize(target);
};

// 排列与对齐
Sigma.prototype.alignLeft = function (keys) {
  this.core.align('left', keys);
};
Sigma.prototype.alignRight = function (keys) {
  this.core.align('right', keys);
};
Sigma.prototype.alignTop = function (keys) {
  this.core.align('top', keys);
};
Sigma.prototype.alignBottom = function (keys) {
  this.core.align('bottom', keys);
};
Sigma.prototype.arrangeRow = function (step, keys) {
  this.core.arrange('row', step, keys);
};
Sigma.prototype.arrangeCol = function (step, keys) {
  this.core.arrange('col', step, keys);
};
Sigma.prototype.arrangeSquare = function (step, keys) {
  this.core.arrange('square', step, keys);
};
/**
 * 向 vnode 插入自定义节点
 * @param {string} vkey vnode的 key 值
 * @param {string} child 要插入的 html 节点
 * @param {*} opts = {
 *   container = '.position', // container为元素要插入位置的类名，默认为 '.position'
 *   element = 'foreignObject', // 自定义节点的类型，默认为 foreignObject
 *   isRemoveExisted = true,  // 默认删除之前插入的节点
 *   ...attr  // attr 为传入的属性，可以自定义 width,height,x,y 等
 * } 配置选项
 * @returns
 */
Sigma.prototype.appendChild = function (
  vkey,
  child,
  opts = {},
) {
  const {
    container = '.position',
    element = 'foreignObject',
    isRemoveExisted = true,
    ...attr
  } = opts;
  const [vnode] = this.core._getShapesByKeys([vkey]);
  if (!vnode) {
    console.error('appendChild: 该元素不存在！');
    return;
  }
  const { component } = vnode;
  if (!component) {
    console.error('appendChild: component 为空，元素未初始化！');
    return;
  }
  const wrapper = component.findOne(container);
  if (!wrapper) {
    console.error('appendChild: 当前container不存在！');
    return;
  }
  const defaultClassName = 'slot-child';
  if (isRemoveExisted) {
    const existed = wrapper.findOne(`.${defaultClassName}`);
    existed?.remove();
  }
  const { width, height } = wrapper.bbox();
  const styles = { width, height, ...attr };
  const el = wrapper.element(element).attr(styles);
  el.addClass(defaultClassName);
  el.add(SVG(child));
};
/**
 * 向 vnode 绑定自定义置顶组件
 * @param {*} vkey
 * @param {*} child
 * @param {*} opts
 * @returns
 */
Sigma.prototype.bindStickyUnit = function (vkey, child, opts = {}) {
  const [vnode] = this.core._getShapesByKeys([vkey]);
  if (!vnode) {
    console.error('appendChild: 该元素不存在！');
    return;
  }
  const stickyUnit = new VnodeStickyUnit({
    vnode,
    child,
    ...opts,
  });
  return stickyUnit;
};

/**
 * 新建连线
 * @param {string} startKey 连线起始节点的 key
 * @param {string} endKey 连线结束节点的 key
 * @param {string} arrowType 'none' | 'start' | 'end' | 'both'，连线的箭头类型
 * @param {string} lineType 'straight' | 'polyline' ，连线的箭头类型
 */
Sigma.prototype.createLineNode = function (options) {
  const lineShapeInstance = new LineShape();
  const vnode = lineShapeInstance.createByVnode(options);
  this.core.add([vnode]);
  return vnode;
};

Sigma.prototype.setUneditable = function () {
  return this.core.__handleEditable(true);
};

Sigma.prototype.setEditable = function () {
  return this.core.__handleEditable(false);
};

Sigma.prototype.toggleEditable = function () {
  const { uneditable } = store.getState();
  this.core.__handleEditable(!uneditable);
  return !uneditable;
};

Sigma.prototype.setGlobalValue = function (value) {
  store.dispatch({
    type: SET_ROOT_ACTION,
    value,
  });
};

Sigma.prototype.getGlobalValue = function () {
  const {
    root,
    mode,
    scale,
    w,
    h,
    doc,
    g,
    defs,
    viewBox,
    container,
    uneditable,
    core,
  } = store.getState();
  const { shapes } = core.data;
  return {
    root,
    mode,
    scale,
    w,
    h,
    doc,
    g,
    defs,
    viewBox,
    container,
    uneditable,
    shapes,
  };
};

Sigma.prototype.transformPoint = function (targetMode, point, exact = true, offsetX = 0, offsetY = 0) {
  const fn = targetMode === SIGMA_GRAPH_MODE_3D ? transform2DTo3D : transform3DTo2D;
  return fn(point, exact, offsetX, offsetY);
};

Sigma.prototype.toggleShapeChecked = function (vnodes) {
  this.core._toggleShapeChecked(vnodes);
};
Sigma.prototype.setShapeChecked = function (vnodes, is) {
  this.core._setShapeChecked(vnodes, is);
};

Sigma.prototype.getCheckedShapes = function (options = {}) {
  return this.core._getCheckedShapes(options);
};

// 清除本地存储的历史记录
Sigma.prototype.clearHistory = function () {
  this.core.clearHistory();
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      history: {
        historical: [], // 历史数据及行为记录
        pointer: -1, // 时间回溯指针
      },
    },
  });
};

/**
 * 给节点生成关联label
 * @param {*} vnode label关联的node
 * @param {*} name label文案，没有则默认用vnode的name
 */
Sigma.prototype.createNodeLabel = function (vnode, name, opt) {
  return this.core._createNodeLabel(vnode, name, opt);
};

Sigma.prototype.setConnectionNumber = function (number) {
  const n = [16, 8, 4].includes(number) ? number : 16;
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      connectionNumber: n,
    },
  });
};

/**
 * 获取当前画布模式是2d还是3d
 * @returns
 */
Sigma.prototype.getMode = function () {
  return  store.getState().mode;
};

Sigma.prototype.setDispersion = function (type) {
  this.core.dispersion(type);
};

Sigma.prototype.transform3DTo2D = transform3DTo2D;
Sigma.prototype.transform2DTo3D = transform2DTo3D;
Sigma.prototype.getStandardPosition = function (position) {
  return this.core.__getStandardPosition(position);
};

// 移动画布使某个节点到画布中心
Sigma.prototype.zoomNodeToMid = function (key, containerId = 'sigma-container', opt = {
  isSetScale: true,
  duration: 500,
}) {
  const { scale } = store.getState();
  const container = document.getElementById(containerId);
  if (!container) return;
  const domRect = container.querySelector(`[id="${key}"]`);
  if (!domRect) return;
  const rect = domRect.getBoundingClientRect();
  const { x, y, width, height } = rect;

  const { viewBox, doc } = store.getState();
  const { x: vx, y: vy, w: vw, h: vh } = viewBox;
  // 计算container距离屏幕上左的距离
  const { x: left, y: top } = document.getElementById(containerId)?.getBoundingClientRect();
  const offsetX = (x + width / 2 - (left ?? 48)) * scale;
  const offsetY = (y + height / 2 - (top ?? 96)) * scale;
  // 需要x y移动距离
  const moveX = viewBox.w / 2 - offsetX;
  const moveY = viewBox.h / 2 - offsetY;

  const nextViewBox = { x: vx - moveX, y: vy - moveY, w: vw, h: vh };
  const nextViewBoxstr = `${vx - moveX} ${vy - moveY} ${vw} ${vh}`;
  store.dispatch({
    type: SET_ROOT_ACTION,

    value: {
      viewBox: nextViewBox,
    },
  });
  // 已动画的形式渐变而不是直接设置
  doc.animate({
    duration: opt.duration,
    when: 'now',
  }).attr({ viewBox: nextViewBoxstr })
    .after(() => {
      if (scale !== 1 && opt?.isSetScale) {
        this.setScale(1);
      }
    });
};

// 将长方形对应的图形，聚焦于画布中心，传递需要是2d的坐标
Sigma.prototype.zoomNodesMiddle = function ({
  left,
  right,
  top,
  bottom,
}) {
  let [centerX, centerY] = [left + (right - left) / 2, top + (bottom - top) / 2];
  const width = right - left;
  const height = bottom - top;
  const { scale, mode } = store.getState();
  if (mode === SIGMA_GRAPH_MODE_3D) {
    const { x, y } = transform2DTo3D({ x: centerX, y: centerY });
    centerX = x;
    centerY = y;
  }
  const { viewBox, doc } = store.getState();
  const { w: vw, h: vh } = viewBox;
  const nx = centerX - (vw / 2);
  const ny = centerY - (vh / 2);
  const nextViewBox = { x: nx, y: ny, w: vw, h: vh };
  const nextViewBoxstr = `${nx} ${ny} ${vw} ${vh}`;
  const newScale = width > height * (vw / vh)
    ? width / (0.8 * vw) * scale
    : height / (0.8 * vh) * scale;
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      viewBox: nextViewBox,
    },
  });
  doc.animate({
    duration: 500,
    when: 'now',
  }).attr({ viewBox: nextViewBoxstr })
    .after(() => this.setScale(newScale > 1 ? newScale : 1, () => {}, { maxScale: Infinity }));
};

// 移动画布使某个节点到画布显示区域中心
Sigma.prototype.zoomNodeToPosition = function (key, {
  containerId = 'sigma-container',
  rightWidth = 0, // 右侧内容宽度，可能是抽屉、浮层等内容，默认0
  needScale = true, // 是否需要缩放到1，默认true
}) {
  const { scale } = store.getState();
  const container = document.getElementById(containerId);
  if (!container) return;
  const domRect = container.querySelector(`[id="${key}"]`);
  if (!domRect) return;
  const rect = domRect.getBoundingClientRect();
  const { x, y, width, height } = rect;

  const { viewBox, doc } = store.getState();
  const { x: vx, y: vy, w: vw, h: vh } = viewBox;
  // 计算container距离屏幕上左的距离
  const { x: left, y: top } = container?.getBoundingClientRect();
  const offsetX = (x + width / 2 - (left ?? 48)) * scale;
  const offsetY = (y + height / 2 - (top ?? 96)) * scale;
  // 需要x y移动距离
  const moveX = (viewBox.w - rightWidth) / 2 - offsetX;
  const moveY = viewBox.h / 2 - offsetY;

  const nextViewBox = { x: vx - moveX, y: vy - moveY, w: vw, h: vh };
  const nextViewBoxstr = `${vx - moveX} ${vy - moveY} ${vw} ${vh}`;
  store.dispatch({
    type: SET_ROOT_ACTION,

    value: {
      viewBox: nextViewBox,
    },
  });
  // 已动画的形式渐变而不是直接设置
  doc.animate({
    duration: 500,
    when: 'now',
  }).attr({ viewBox: nextViewBoxstr })
    .after(() => {
      if (scale !== 1 && needScale) {
        this.setScale(1);
      }
    });
};

/**
 * 当节点不显示在画布时，将节点水平移动，使其在可视区域内
 */
Sigma.prototype.borderMove = function (leftX, rightX, offsetX = 0, containerId = 'sigma-container') {
  const container = document.getElementById(containerId);
  if (!container) return;
  const { viewBox, doc } = store.getState();
  const { x: vx, y: vy, w: vw, h: vh } = viewBox;
  if (leftX >= vx && rightX <= (vx + vw)) {
    return;
  }
  const isLeftMove = leftX < vx;
  const isRightMove = rightX > (vx + vw);
  let moveX = vx;
  if (isLeftMove) {
    moveX = leftX - offsetX;
  } else if (isRightMove) {
    moveX = vx + rightX - (vx + vw) + offsetX;
  }
  const nextViewBox = { x: moveX, y: vy, w: vw, h: vh };
  const nextViewBoxstr = `${moveX} ${vy} ${vw} ${vh}`;
  store.dispatch({
    type: SET_ROOT_ACTION,
    value: {
      viewBox: nextViewBox,
    },
  });
  doc.animate({
    duration: 500,
    when: 'now',
  }).attr({ viewBox: nextViewBoxstr });
};

Sigma.prototype.setText = function (text, key) {
  this.core.setText(text, key);
};

Sigma.prototype.copy = function () {
  this.core.copy();
};

Sigma.prototype.paste = function () {
  this.core.paste();
};

Sigma.prototype.alignmentLinesClear = function () {
  // 清除对齐线
  this.core.lines.clear();
};

Sigma.prototype.onlyShapeChecked = function (vnodes) {
  this.core.onlyShapeChecked(vnodes);
};


export default Sigma;
export * from './methods';
