{"name": "@tencent/tea-sdk-capacity-monitoring", "version": "2.0.8", "description": "The capacity-monitoring tea sdk for qcloud console", "main": "src/app.js", "scripts": {"dev": "CI=1 tea dev", "scan": "tea scan", "build": "tea build", "deploy": "tea commit", "build-types": "rm -rf dts/types && npx tsc -d --emitDeclarationOnly --skipLibCheck --declarationDir dts/types", "publish-types": "cd dts && npm version patch && tnpm publish", "fix": "eslint --ext .js,.jsx,.tsx,.ts ./src --fix"}, "keywords": ["tea", "sdk", "capacity-monitoring"], "engines": {"typescript": ">3.3", "node": ">=16.0.0 <19.0.0"}, "license": "UNLICENSED", "dependencies": {"@hookstate/core": "^4.0.1", "@tencent/sigma-editor": "workspace:*", "@tencent/sigma-editor-for-server": "workspace:*", "@tencent/tea-app": "^2.1.22", "@tencent/tea-chart": "^2.5.0-alpha.8", "@tencent/tea-component": "2.7.9", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "js-base64": "^3.7.7", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "moment": "^2.30.1", "pako": "^2.1.0", "rc-slider": "^11.1.5", "react": "^18.3.1", "react-dom": "^18.3.1", "swr": "^2.2.5", "tdesign-react": "^1.7.7", "vite-template": "workspace:*"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@tencent/eslint-config-tencent": "^1.0.4", "@tencent/eslint-plugin-tea-i18n": "^0.1.16", "@tencent/tea-scripts": "^2.1.24", "@tencent/tea-types": "^0.1.15", "@types/moment": "^2.13.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^5.5.0", "axios": "^1.7.2", "babel-loader": "^8.2.3", "eslint": "^8.57.0", "eslint-plugin-react": "^7.21.5", "postcss": "^8.4.31", "postcss-scss": "^4.0.9", "sass": "^1.69.5", "sass-loader": "^10.3.2", "typescript": "^4.3.5"}, "volta": {"node": "18.20.4"}}