.capacity-monitoring-opacity40 {
  opacity: 0.4;
}
.capacity-monitoring-wrap {
  font-family: Roboto,San Francisco,Helvetica Neue,Helvetica,Arial,PingFangSC-Light,Hiragina Sans GB,WenQuanYi Micro Hei,microsoft yahei ui,microsoft yahei,sans-serif;
  .tea-icon {
    background-size: auto;
    position: relative;
    top: 0;
  }
  .is-loading .tea-icon-loading {
    background-size: auto;
  }
}
.capacity-monitoring-sdk-overlay-root {
  background-color: red;
  .tea-backdrop {
    opacity: inherit;
  }
  .tea-overlay {
    z-index: 1001;
  }
}
.inspection-handle-list {
  width: 110px;
  & > li {
    &{
      display: flex!important;
      align-items: center;
      color: #C1C6C8!important;
    }
    &:hover {
      color: #fff;
      background-color: #006EFF!important;
    }
  }
  @keyframes Routes {
    0% {
      transform: rotate(0);
    }
    100% {
      transform: rotate(1turn);
    }
  }
  svg {
    margin-right: 5px;
    path {
      fill: #C1C6C8;
    }
  }
  .loading-icon {
    animation: Routes 1s linear infinite;
  }
  .inspecting-icon {
    animation: Routes 1s linear infinite;
  }
}