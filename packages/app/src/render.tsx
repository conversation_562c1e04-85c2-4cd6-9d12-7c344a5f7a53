import React from 'react';
import { message } from '@tencent/tea-component';
import { cloneDeep } from 'lodash-es';
import NoResourceModal from '@src/components/no-resource-modal';
import { reportEvent, EVENT } from '@src/utils/report';
import CapacityMonitoring from './pages/capacity-monitoring';
import ArchiveReport from './components/archive-report';
import HandleArea from './pages/handle-area';
import { ShapeTypeEnum } from './constants/sigmaEditor';
import globalState from '@src/stores/global.state';
import {
  nodeDataInfo,
  setNodeDataInfo,
  setNodeOriginInfo,
  nodeConfigInfo,
  // setNodeConfigInfo,
  setArchInfo,
} from '@src/utils/caching';
import './style/index.less';
// import { describeCapacityProductList } from '@src/api/thresholdsSetting';
import { t } from '@tea/app/i18n';

export interface DefaultCardProps {
  serviceType?: string;
}

/**
 * 检查是否空对象
 *
 * @param {object} obj - 对象
 * @returns {boolean} 布尔值
 */
function isEmptyObject(obj: object): boolean {
  return (obj)?.constructor === Object && Reflect.ownKeys(obj).length === 0;
}

/**
 * 根据supportedNodes将结构图中的不支持的节点修改透明度
 *
 * @param {object} supportedNodes -  容量支持的产品列表
 * @param {object} arcNodes - 结构图上的节点列表
 * @returns {void}
 */
const transformNodeByConfigInfo = (supportedNodes, arcNodes, props) => {
  if (supportedNodes && arcNodes && !isEmptyObject(arcNodes) && !isEmptyObject(supportedNodes)) {
    const supported = supportedNodes?.ProductInfos?.map(item => item?.Product?.toLowerCase()) || [];
    const allNodes = props.getArchNodes();
    // eslint-disable-next-line no-restricted-syntax
    for (const key in allNodes) {
      if (allNodes[key].type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
        if (!supported.includes(allNodes[key]?.name?.toLowerCase())) {
          props.addNodeClass([key], 'capacity-monitoring-opacity40');
        }
      }
    }
  }
};

/**
 * 渲染创建者组件的函数。
 *
 * 它设置架构信息，检查URL参数中的nodeId，
 * 如果找到有效的节点，则打开带有容量监控组件的抽屉。
 * 同时调用获取架构详情和节点配置信息的函数。
 *
 * @param {object} props - 传递给组件的属性。
 * @return {object} 包含处理形状点击和文档点击事件的函数的对象。
 */
const renderCreator = (props: AppPluginAPI.PluginAPI) => {
  globalState.set(state => ({
    ...state,
    env: props?.env ?? '',
    sessionId: props?.getPluginSessionId?.() ?? '',
    archId: props?.archInfo?.archId ?? '',
  }));
  // 每次初始化后 全局设置预测抽屉开展状态为false
  if (globalState.get().predictionOpened) {
    globalState.set(state => ({
      ...state,
      predictionOpened: false,
    }));
  }
  setArchInfo(props);
  if (props.env !== 'ISA') {
    document
      .querySelector('#tea-overlay-root')
      ?.classList.add('capacity-monitoring-sdk-overlay-root');
  }
  /**
   * 异步获取业务架构详情
   *
   * 此函数通过调用DescribeArch服务来获取指定业务架构的详细信息，包括节点列表等
   * 如果获取过程中出现错误，会弹出错误提示
   * 如果节点列表为空，会设置一个提示组件，提示用户先为业务架构绑定资源
   * 如果成功获取到节点列表，会更新节点数据信息，并设置处理区域的组件
   *
   * @param mapId 业务架构的ID
   * @param userName 用户名，用于服务调用时的身份验证
   */
  const getDescribeArch = async () => {
    try {
      const archInfo = cloneDeep(await props.getCommonData(20000));
      // 过滤节点列表，移除BindingType未定义的节点
      (archInfo?.ArchNodeList && (archInfo.ArchNodeList = archInfo.ArchNodeList.filter(item => item.BindingType !== undefined)));
      // 如果过滤后的节点列表为空，设置一个提示组件
      if (archInfo?.ArchNodeList?.length === 0) {
        props.setSlotComponent(<NoResourceModal
            content={t('需要您先为业务架构绑定资源后才能进行容量监测操作和监测结果查看')}
          />);
        props.initOver();
        return;
      }
      // 更新节点数据信息
      setNodeDataInfo(archInfo);
      props.initOver();
      props.showTools();
      // 设置处理区域的组件
      props.setSlotComponent(<HandleArea arInfo={props} transformNodeByConfigInfo={() => {
        transformNodeByConfigInfo(nodeConfigInfo, archInfo, props); // 根据配置信息转换节点透明度
      }} />);
    } catch (err) {
      props.initOver();
      console.log(err);
    }
  };

  props?.archInfo?.archId && getDescribeArch();

  const getNodeName = (params) => {
    const { customize, type } = params?.node;
    const { cluster, clusterName, namespace, workloadType, label } = customize;
    if (type === 'SIGMA_TKE_SHAPE' || cluster) {
      switch (workloadType) {
        case 'TKE Deployment': {
          return `Deployment：${label}`;
          break;
        }
        case 'TKE Service': {
          return `Service：${label}`;
          break;
        }
        default: {
          return `${clusterName}：${namespace}`;
          break;
        };
      }
    }
    return label || params?.node?.name || params?.node?.cName;
  };

  return {
    reportComponent: <ArchiveReport visible={true} digitalAssets={true} {...props} />,
    /**
     * 处理形状点击事件，检查节点数据和配置信息是否可用，
     * 并且如果节点是有效的产品形状，则打开容量监控抽屉。
     *
     * @param {object} params - 形状点击事件的参数，包括节点信息。
     * @return {void}
     */
    onShapeClick: (params) => {
      if (
        Object.keys(nodeDataInfo).length === 0
        || Object.keys(nodeConfigInfo).length === 0
      ) {
        return;
      }
      // eslint-disable-next-line max-len
      if (
        !nodeConfigInfo.ProductInfos?.map(item => item.Product.toLowerCase()).includes(params.node?.name.toLowerCase())
      ) {
        if (params?.node?.type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
          message.error({
            content: t('暂不支持对该类型节点进行容量监测'),
            className: 'no-contain-task-wrap',
          });
        }
        return;
      }
      const contain = nodeDataInfo.ArchNodeList.some(item => item.DiagramId === params.node?.key);
      if (!contain) {
        (document as any)
          .querySelector('.no-contain-task-wrap .sdk-menus-icon-close')
          ?.click();
        message.error({
          content: t('请您先为业务架构绑定资源，之后即可对业务监控进行容量监测。'),
        });
        return;
      }
      // 如果处于 容量预测 选择图元模式
      if (globalState.get().chooseFigureMetropolitanMode) {
        const { node } = params;
        globalState.set(state => ({
          ...state,
          currentChooseFigure: {
            NodeUuid: node?.key,
            ItemName: `${node?.name}`, // 产品名称
            // eslint-disable-next-line max-len
            Label: node?.customize?.label || node?.styles?.label?.value || node?.styles?.label?.default || node?.cName, // 图元名称
          },
        }));
      }
      if (globalState.get().predictionOpened) {
        // 预测抽屉打开的情况下不弹出详情抽屉 & 高亮对应预测结果
        const nodeKey = params?.node?.key;
        if (nodeKey) {
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
          const hightDom = document.querySelectorAll('.data-hight') as NodeListOf<HTMLElement>;
          // eslint-disable-next-line no-param-reassign
          hightDom.forEach(item => item.style.border = '1px solid #cfd5de');
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
          const node = document.querySelector(`[data-hight-key="${nodeKey}"]`) as HTMLElement;
          if (node) {
            globalState.set(state => ({
              ...state,
              predictionHightNodeKey: nodeKey,
            }));
            setTimeout(() => {
              node.scrollIntoView();
            }, 200);
            node.style.border = '1px solid rgba(0, 110, 255, 1)';
          };
        }
        return false;
      }
      setNodeOriginInfo(params);
      props.openDrawer();
      reportEvent({
        key: EVENT.CHECK_NODE_DETAIL,
        extraInfo: null,
      });
      props.setDrawerProps({
        title: t('{{nodeName}}', {
          nodeName: getNodeName(params),
        }),
        className: 'capacityMonitoringDrawer',
        extra: {
          outerClickClosable: false,
        },
        children: (
          <CapacityMonitoring
            arInfo={props}
            nodeInfo={params}
            key={params.node?.key}
          />
        ),
      });
    },
    // eslint-disable-next-line
    onDocClick: () => {
      if (!globalState.get().predictionOpened) {
        props.closeDrawer();
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const hightDom = document.querySelectorAll('.data-hight') as NodeListOf<HTMLElement>;
        // eslint-disable-next-line no-param-reassign
        hightDom.forEach(item => item.style.border = '1px solid #cfd5de');
      }
    },
  };
};

// const renderCreator = props => {
//   return <ConfigProvider>
//     {/*<CloudInspectionCard arInfo={props}/>*/}
//     <HandleInspectionArea arInfo={props}/>
//   </ConfigProvider>;
// };

export default {
  init: renderCreator, // 最近一次云顾问巡检详情卡片
  // eslint-disable-next-line
  destroy: () => {},
};
