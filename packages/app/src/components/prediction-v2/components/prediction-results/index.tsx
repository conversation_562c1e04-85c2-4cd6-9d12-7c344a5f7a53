import React from 'react';
import { Collapse, Text, Row, Col, Tabs, TabPanel } from '@tencent/tea-component';
import { Collapse as TCollapse } from 'tdesign-react';
import { t, Slot } from '@tea/app/i18n';
import { reportEvent, EVENT } from '@src/utils/report';
import success from '@src/statics/svg/success.svg';
import ins from '@src/statics/svg/ins.svg';
import DetailCard from '../card';
import s from './index.module.scss';

const { Panel } = TCollapse;

interface IProps {
  predictionResult: Record<string, any>;
  setPredictionResult: (result: Record<string, any>) => void;
  doneDefaultActiveIds: string[];
  onPosition: (data?: any) => void;
  archInfo: any;
  PositionIcon: React.ComponentType<any>;
}

/**
 * 预测结果组件
 * @param {Record<string, any>} predictionResult - 预测结果数据
 * @param {function} setPredictionResult - 设置预测结果的方法
 * @param {string[]} doneDefaultActiveIds - 默认展开的面板ID
 * @param {function} onPosition - 定位回调函数
 * @param {any} archInfo - 架构信息
 * @param {React.ComponentType<any>} PositionIcon - 定位图标组件
 * @returns React.ReactElement
 */
export default function PredictionResults(props: IProps): React.ReactElement {
  const {
    predictionResult,
    setPredictionResult,
    doneDefaultActiveIds,
    onPosition,
    archInfo,
    PositionIcon,
  } = props;

  return (
    <div className={s.block}>
      <p className={s.header}>{t('查看结果')}</p>
      <div style={{ marginTop: 10 }}>
        <Collapse
          defaultActiveIds={doneDefaultActiveIds}
          key={JSON.stringify(doneDefaultActiveIds)}
          className={s.collapse}
        >
          {(predictionResult?.PredictionResultInfos ?? []).map((v, index) => (
              <Collapse.Panel
                id={`${index}`}
                title={v?.ProductName || '-'}
                key={`PredictionNodeInfos-done-${index}`}
              >
                {(v.PredictionNodeIdInfos ?? []).map((v2, i2) => (
                  <div
                    style={{
                      border: '1px solid #CFD5DE',
                      marginTop: 10,
                    }}
                    key={`${v2.NodeUuid}-${i2}`}
                    className='data-hight'
                    data-hight-key={v2.NodeUuid}
                  >
                    <TCollapse
                      borderless
                      expandIcon
                      value={v2.expanded ? [1] : []}
                      expandIconPlacement={'right'}
                      expandOnRowClick
                      onChange={(v) => {
                        v2.expanded = !!v.length;
                        reportEvent({
                          key: EVENT.CLICK_PREDICTED_CARD,
                          extraInfo: `${v2.expanded}`,
                        });
                        // 折叠后关闭定位
                        if (!v2.expanded) {
                          v2.position = false;
                          onPosition(predictionResult?.PredictionResultInfos);
                        }
                        setPredictionResult({ ...predictionResult });
                      }}
                      className={s.tCollapse}
                    >
                      <Panel
                        header={
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              width: '100%',
                            }}
                          >
                            <div className={s.tCollapseTitle}>
                              <span>{t('图元名称')}</span>
                              <Text
                                overflow
                                style={{ marginLeft: 30, maxWidth: 220 }}
                                tooltip={v2?.ItemName}
                                verticalAlign="middle"
                              >
                                {v2?.ItemName}
                              </Text>
                              {v2.expanded ? (
                                !v2?.position ? (
                                  <span
                                    className={s.tPosition}
                                    onClick={(e) => {
                                      reportEvent({
                                        key: EVENT.LOCATE_PREDICTED_NODE,
                                        extraInfo: v2?.NodeUuid,
                                      });
                                      v2.position = true;
                                      setPredictionResult({
                                        ...predictionResult,
                                      });
                                      archInfo.zoomNodeToPosition(v2?.NodeUuid, {
                                        rightWidth: 490,
                                      });
                                      onPosition(predictionResult?.PredictionResultInfos);
                                      e.stopPropagation();
                                    }}
                                  >
                                    <PositionIcon
                                      style={{ marginLeft: 10, height: 12 }}
                                    />
                                    {t('定位')}
                                  </span>
                                ) : (
                                  <span
                                    className={s.tStopPosition}
                                    onClick={(e) => {
                                      // eslint-disable-next-line no-param-reassign
                                      v2.position = false;
                                      setPredictionResult({
                                        ...predictionResult,
                                      });
                                      onPosition(predictionResult?.PredictionResultInfos);
                                      e.stopPropagation();
                                    }}
                                  >
                                    {t('停止定位')}
                                  </span>
                                )
                              ) : null}
                            </div>
                            {!v2.expanded ? (
                              v2.risk ? (
                                <span
                                  style={{
                                    fontWeight: 400,
                                  }}
                                >
                                  <span style={{ color: '#E54545' }}>
                                    <Slot content={v2.risk} />{' '}
                                  </span>{' '}
                                  {t('个风险实例')}
                                </span>
                              ) : (
                                <span
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    fontWeight: 400,
                                  }}
                                >
                                  {t('正常负载')}{' '}
                                  <img
                                    style={{ marginLeft: 8 }}
                                    src={success}
                                  />
                                </span>
                              )
                            ) : null}
                          </div>
                        }
                      >
                        <div style={{ padding: '0px 10px 10px 10px' }}>
                          <Row>
                            <Col span={12}>
                              <div
                                className={s.row}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <p>{t('绑定实例数')}</p>
                                <p>{v2?.Count}</p>
                              </div>
                            </Col>
                            <Col span={12}>
                              <div className={s.row}>
                                <p>{t('放量倍数')}</p>
                                <p>{v2?.ResourceTimes}</p>
                              </div>
                            </Col>
                          </Row>
                          <div>
                            {(v2?.InsTypeLists ?? []).filter(v => v?.ItemName).length > 1 ? (
                              <Tabs
                                style={{ marginTop: 5 }}
                                className={s.tabs}
                                tabs={(v2?.InsTypeLists ?? [])
                                  .filter(v => v?.ItemName)
                                  .map((v, i) => ({
                                    label: (
                                      <span
                                        style={{
                                          fontSize: 12,
                                          fontWeight: 500,
                                          display: 'flex',
                                          alignItems: 'center',
                                        }}
                                      >
                                        {v?.ItemName}
                                        <img
                                          style={{ margin: '0 3px 0 5px' }}
                                          src={ins}
                                        />
                                        <span>
                                          {v?.Count || 0}
                                        </span>
                                      </span>
                                    ),
                                    id: `${v?.ItemName}-${i}`,
                                  }))}
                              >
                                {(v2?.InsTypeLists ?? [])
                                  .filter(v => v?.ItemName)
                                  .map((d, i) => (
                                    <TabPanel
                                      id={`${d.ItemName}-${i}`}
                                      key={`${d.ItemName}-${i}`}
                                    >
                                      <DetailCard
                                        metric={{
                                          algorithmType: v2?.AlgorithmType,
                                          periodValue: v2?.PeriodValue,
                                        }}
                                        data={d}
                                        style={{
                                          marginTop: 0,
                                          paddingTop: 0,
                                          paddingBottom: 0,
                                        }}
                                      />
                                    </TabPanel>
                                  ))}
                              </Tabs>
                            ) : (
                              <DetailCard
                                metric={{
                                  algorithmType: v2?.AlgorithmType,
                                  periodValue: v2?.PeriodValue,
                                }}
                                data={(v2?.InsTypeLists ?? [])[0]}
                              />
                            )}
                          </div>
                        </div>
                      </Panel>
                    </TCollapse>
                  </div>
                ))}
              </Collapse.Panel>
          ))}
        </Collapse>
      </div>
    </div>
  );
}
