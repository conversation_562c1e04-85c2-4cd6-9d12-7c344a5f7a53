@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.loading {
  animation: rotate 1s linear infinite;
  transform-origin: center center;
}
.download {
  position: relative;
  &:hover {
    cursor: pointer;
    .download-options {
      display: block;
    }
  }
  .download-mask {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
  .download-options {
    display: none;
    position: absolute;
    background: #fff;
    left: 15px;
    width: 120px;
  }
  .download-option {
    display: flex;
    padding: 5px 27px 5px 10px;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    &:hover {
      cursor: pointer;
      background-color: #f5f5f5;
    }
  }
}
.download-report {
  display: flex;
  height: 30px;
  padding: 0px 10px;
  align-items: center;
  gap: 5px;
  border-radius: 15px;
  background: #363A50;
  color: #fff;
  margin-left: 15px;
}
.box {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 44px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    .wrapper {
      display: flex;
      align-items: center;
    }
    .rotate {
      display: flex;
      width: 30px;
      height: 30px;
      justify-content: center;
      align-items: center;
      border-radius: 15px;
      margin-left: 15px;
      background: var(--color-bg-4, #363A50);
      &:hover {
        cursor: pointer;
      }
    }
    .left {
      display: flex;
      align-items: center;
      gap: 10px;
      border-radius: 15px;
      background: var(--color-bg-4, #F3F4F7);
      color: var(---, rgba(0, 0, 0, 0.60));
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
    .hover {
      &:hover {
        cursor: pointer;
        background-color: #0052d9;
      }
    }
    .title {
      border-radius: 15px;
      background: var(---4, #363A50);
      display: flex;
      height: 30px;
      padding: 0px 10px;
      align-items: center;
      gap: 5px;
      color: #fff;

      img {
        width: 16px;
        height: 16px;
      }

      span {
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
    }

    .closeIcon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }
}