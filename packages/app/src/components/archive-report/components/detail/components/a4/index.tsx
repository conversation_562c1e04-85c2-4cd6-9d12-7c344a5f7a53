/* eslint-disable @typescript-eslint/naming-convention */
import React, { useState, useEffect } from 'react';
import globalState from '@src/stores/global.state';
import SigmaComponent from '../../../sigma';
import Chart from '../../../detail-chart';
import Honeycomb from '../../../honeycomb';
import Table from '../../../detail-table';
import left from '../../../../statics/left.svg';
import logo from '../../../../statics/logo.svg';
import uninvolved from '../../../../statics/uninvolved.svg';
import Header from '../../../header';
import Skeleton from '../../../skeleton';
import { RiskColor } from '../../../../../../constants/color';
import s from './index.module.scss';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  useWindowResize,
  calcRateStyle,
} from '../../../../hooks/useWindowResize';
import { ReportView, ReportType, CcnConfig } from '../../../../constants';

interface ArchiveReportDetailProps {
  Sigma: any;
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangePage?: () => void;
  view?: ReportView;
  onChangeView?: () => void;
  print?: boolean;
  data?: any;
  activeKey: string;
  setActiveKey?: (key: string) => void;
  exportReport?: (success: () => void, error: () => void) => void;
  message?: any;
  digitalAssets?: boolean;
}

const ArchiveReportDetail: React.FC<ArchiveReportDetailProps> = (props) => {
  const {
    Sigma,
    onClose = () => undefined,
    visible,
    arInfo,
    arcData,
    onChangePage,
    view,
    onChangeView,
    print = false,
    data,
    activeKey,
    setActiveKey,
    exportReport,
    message,
    digitalAssets = false,
  } = props;
  const [loading, setLoading] = useState(true);
  const { screenRef } = useWindowResize();
  // 生命周期
  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const scaleStyle = print
    ? {}
    : calcRateStyle();
  const item = data?.NodeLoadDetailItems?.find((item: any) => item?.NodeUuid === activeKey) || {};
  const instanceLoadInfosFilter = item?.InstanceLoadInfos?.filter(v => v?.InstanceType !== '');

  const isCcn = item?.Product?.toLowerCase?.() === CcnConfig.CCNS;
  // 运营端下载
  const onDownload = (type: ReportType, downloadHash: string) => {
    globalState.exportState.set(state => ({
      ...state,
      downloadData: {
        type,
        downloadHash,
      },
    }));
  };

  return (
    visible && (
      <>
        {!digitalAssets && <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
        ></div>}
        <div
          className={s.container}
          id="archive-report"
          ref={(print || digitalAssets) ? null : screenRef}
          style={digitalAssets ? {
            position: 'static',
            transform: 'translate(18%, 0%)',
          } : scaleStyle}
        >
          {(!print && !digitalAssets) && (
            <Header
              onClose={onClose}
              archiveStatus={data?.ArchiveStatus}
              url={data?.DigitalAssetUrl}
              reportFile={data?.ReportFile}
              excelFile={data?.ExcelFile}
              arInfo={arInfo}
              onChangeView={onChangeView}
              exportReport={exportReport}
              onDownload={onDownload}
            />
          )}
          <div className={s.box}>
            <p
              className={s.header}
              style={print ? { width: '100%' } : {}}
              onClick={onChangePage}
            >
              <img src={print ? logo : left} alt={t('icon')} />
              <span style={{ marginLeft: 5 }}>
                {!print
                  ? t('返回概览')
                  : `${data?.Title}`}
              </span>
            </p>
            <div className={s.content} style={print ? {} : {}}>
              <div className={s.sigma} style={{ height: 260, width: 502 }}>
                <SigmaComponent
                  digitalAssets={digitalAssets}
                  Sigma={Sigma}
                  arcData={arcData}
                  width={502}
                  height={260}
                  id="sigma-detail"
                  data={data}
                  activeKey={activeKey}
                  setActiveKey={setActiveKey}
                  print={print}
                  arInfo={arInfo}
                  message={message}
                />
              </div>
              {/* 非打印模式 加载中 */}
              {loading && !print && (
                <>
                  <Skeleton type="rect" style={{ width: '30%', opacity: 1 }} />
                  <Skeleton type="rect" style={{ width: '100%', opacity: 1 }} />
                  <Skeleton type="rect" style={{ width: '20%', opacity: 1 }} />
                  <Skeleton
                    type="rect"
                    style={{ width: '30%', opacity: 0.8 }}
                  />
                  <Skeleton
                    type="rect"
                    style={{ width: '100%', opacity: 0.5 }}
                  />
                  <Skeleton
                    type="rect"
                    style={{ width: '20%', opacity: 0.3 }}
                  />
                </>
              )}
              {/* 非加载中 或 打印模式 */}
              {(!loading || (loading && print)) && (
                <>
                  <p className={s.name}>{item?.NodeName || '-'}</p>
                  <div className={s.chart}>
                    <div className={s.desc}>
                      <div className={s.left}>
                        <p>
                          <span>{t('产品类型')}</span>
                          <span>{item?.ProductName}</span>
                        </p>
                        <p>
                          <span>{t('容量分布')}</span>
                          <span className={s.dotBox}>
                            <span className={s.dot} style={{ backgroundColor: RiskColor.High }}></span>
                            <span>{item?.HighLoadCount || 0}</span>
                            <span className={s.dot} style={{ backgroundColor: RiskColor.Medium }}></span>
                            <span>{item?.MediumLoadCount || 0}</span>
                            <span className={s.dot} style={{ backgroundColor: RiskColor.LowUsed }}></span>
                            <span>{item?.LowUsageCount || 0}</span>
                            <span className={s.dot} style={{ backgroundColor: RiskColor.Low }}></span>
                            <span>{item?.LowLoadCount || 0}</span>
                          </span>
                        </p>
                      </div>
                      <div className={s.right}>
                        <p>
                          <span>{t('绑定资源数')}</span>
                          <span>{item?.InstanceCount}</span>
                        </p>
                        <p>
                          <span>{isCcn ? CcnConfig.REPORT_TITLE : t('机型分布')}</span>
                        </p>
                      </div>
                    </div>
                    <div className={s.pie}>
                      <div className={s.left} style={{ width: '48%' }}>
                        <Honeycomb print={print} view={view} data={item?.InstanceLoadInfos || []} product={item?.Product} />
                      </div>
                      <div
                        className={s.right}
                        style={{ width: '52%', marginTop: 3 }}
                      >
                        {instanceLoadInfosFilter?.length ? <Chart data={instanceLoadInfosFilter || []} /> : (<div className={s.uninvolved}>
                          <img src={uninvolved} alt={t('icon')} />
                          <p style={{ fontSize: 12 }}>{t('不涉及')}</p>
                        </div>)}
                      </div>
                    </div>
                    <div className={s.desc}>
                      <div className={s.left}>
                        <p>
                          <span>{t('实例详情')}</span>
                        </p>
                        <p></p>
                      </div>
                      <div className={s.right}></div>
                    </div>
                  </div>
                  <div className={s.table}>
                    <Table data={item?.InstanceLoadInfos || []} print={print} product={item?.Product} />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </>
    )
  );
};

export default ArchiveReportDetail;
