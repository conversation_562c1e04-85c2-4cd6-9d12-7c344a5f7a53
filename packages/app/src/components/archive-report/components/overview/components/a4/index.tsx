/* eslint-disable @typescript-eslint/naming-convention */
import React from 'react';
import globalState from '@src/stores/global.state';
import { t } from '@tencent/tea-app/lib/i18n';
import SigmaComponent from '../../../sigma';
import logo from '../../../../statics/logo.svg';
import right from '../../../../statics/arrow-right.svg';
import rightBlue from '../../../../statics/arrow-right-blue.svg';
import s from './index.module.scss';
import { useWindowResize, calcRateStyle } from '../../../../hooks/useWindowResize';
import Chart from '../../../chart';
import Header from '../../../header';
import { ReportView, ReportType } from '../../../../constants';

interface ArchiveReportOverviewProps {
  Sigma: any;
  onClose?: (unArchived?: boolean) => void;
  visible?: boolean;
  arInfo?: any;
  arcData?: any;
  onChangePage?: () => void;
  view?: ReportView;
  onChangeView?: () => void;
  print?: boolean;
  data?: any;
  exportReport?: (success: () => void, error: () => void) => void;
  message?: any;
  digitalAssets?: boolean;
}

const ArchiveReportOverview: React.FC<ArchiveReportOverviewProps> = (props) => {
  const {
    Sigma,
    onClose = () => undefined,
    visible,
    print = false,
    arInfo,
    arcData,
    onChangePage,
    onChangeView,
    data,
    exportReport,
    message,
    digitalAssets = false,
  } = props;
  const { screenRef } = useWindowResize();

  const scaleStyle = print ? {
    transform: `scale(${window.innerWidth / 580})`,
    left: 0,
    top: 0,
    transformOrigin: 'left top',
    height: 826,
  } : calcRateStyle();

  // 运营端下载
  const onDownload = (type: ReportType, downloadHash: string) => {
    globalState.exportState.set(state => ({
      ...state,
      downloadData: {
        type,
        downloadHash,
      },
    }));
  };

  return (
    visible && (
      <>
        {!digitalAssets && <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: 'rgba(0,0,0,0.2)',
          }}
        ></div>}
        <div
          className={s.container}
          id="archive-report"
          ref={(print || digitalAssets) ? null : screenRef}
          style={digitalAssets ? {
            position: 'static',
            transform: 'translate(18%, 0%)',
          } : scaleStyle}
        >
          {(!print && !digitalAssets) && (
            <Header
              onClose={onClose}
              archiveStatus={data?.ArchiveStatus}
              url={data?.DigitalAssetUrl}
              reportFile={data?.ReportFile}
              excelFile={data?.ExcelFile}
              onDownload={onDownload}
              onChangeView={onChangeView}
              exportReport={exportReport}
              arInfo={arInfo}
            />
          )}
          <div className={s.content}>
            <div className={s.summary}>
              <p className={s.header}>
                <img src={logo} alt="logo" width={20} />
                <span className={s.title}>
                  {data?.Title}
                </span>
              </p>
              <p className={s.desc}>
                {t('Tencent Cloud Smart Advisor Capacity Monitoring Report')}
              </p>
              <div className={s.row} style={{ marginTop: 15 }}>
                <div className={s.col}>
                  <p>{t('架构图名称：')}</p>
                </div>
                <div className={s.col}>
                  <p>{data?.MapName}</p>
                  <span>{data?.MapId}</span>
                </div>
              </div>
              {/* <div className={s.row}>
                <div className={s.col}>
                  <p>{t('客户名称：')}</p>
                </div>
                <div className={s.col}>
                  <p>{data?.CustomerName || '-'}</p>
                </div>
              </div> */}
              <div className={s.row}>
                <div className={s.col}>
                  <p>{t('APPID：')}</p>
                </div>
                <div className={s.col}>
                  <p>{data?.AppId}</p>
                </div>
              </div>
              <div className={s.row}>
                <div className={s.col}>
                  <p>{t('报告生成时间：')}</p>
                </div>
                <div className={s.col}>
                  <p>{data?.CreateTime}</p>
                </div>
              </div>
            </div>
            <div className={s.chart}>
              <div className={s.box}>
                <p className={s.title}>{t('架构图')}</p>
                <div className={s.sigma} style={{ height: 260, width: 502 }}>
                  <SigmaComponent
                    digitalAssets={digitalAssets}
                    Sigma={Sigma}
                    arcData={arcData}
                    data={data}
                    id="sigma-detail"
                    width={502}
                    height={260}
                    arInfo={arInfo}
                    interact={false}
                    message={message}
                  />
                </div>
                <p className={s.title} style={{ marginTop: 10 }}>
                  {t('容量概览')}
                </p>
                <div className={s.echart} style={print ? { marginTop: 10 } : {}}>
                  <Chart
                    print={print}
                    onLegendClick={() => {
                      // console.log('Legend clicked:', name);
                    }}
                    data={data}
                  />
                </div>
                <div className={`${s.footer} ${print ? s.printFooter : ''}`}>
                  <button onClick={onChangePage}>
                    {t('查看详情')}
                    <img src={print ? rightBlue : right} width={12} alt={t('查看详情')} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  );
};

export default ArchiveReportOverview;
